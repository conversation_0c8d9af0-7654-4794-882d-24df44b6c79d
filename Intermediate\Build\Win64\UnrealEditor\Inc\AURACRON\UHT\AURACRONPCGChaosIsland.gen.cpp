// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGChaosIsland.h"
#include "UObject/CoreNet.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGChaosIsland() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIsland();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAURACRONPCGChaosIsland Function ApplyChaosEffect ************************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics
{
	struct AURACRONPCGChaosIsland_eventApplyChaosEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeito ca\xc3\xb3tico ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeito ca\xc3\xb3tico ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventApplyChaosEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "ApplyChaosEffect", Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::AURACRONPCGChaosIsland_eventApplyChaosEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::AURACRONPCGChaosIsland_eventApplyChaosEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execApplyChaosEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyChaosEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function ApplyChaosEffect **************************

// ********** Begin Class AAURACRONPCGChaosIsland Function ApplyEnvironmentalHazardEffect **********
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics
{
	struct AURACRONPCGChaosIsland_eventApplyEnvironmentalHazardEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeitos de perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeitos de perigos ambientais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventApplyEnvironmentalHazardEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "ApplyEnvironmentalHazardEffect", Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::AURACRONPCGChaosIsland_eventApplyEnvironmentalHazardEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::AURACRONPCGChaosIsland_eventApplyEnvironmentalHazardEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execApplyEnvironmentalHazardEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyEnvironmentalHazardEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function ApplyEnvironmentalHazardEffect ************

// ********** Begin Class AAURACRONPCGChaosIsland Function ApplyUnstableTerrainEffect **************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics
{
	struct AURACRONPCGChaosIsland_eventApplyUnstableTerrainEffect_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplica efeitos de terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica efeitos de terreno inst\xc3\xa1vel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventApplyUnstableTerrainEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "ApplyUnstableTerrainEffect", Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::AURACRONPCGChaosIsland_eventApplyUnstableTerrainEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::AURACRONPCGChaosIsland_eventApplyUnstableTerrainEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execApplyUnstableTerrainEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyUnstableTerrainEffect(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function ApplyUnstableTerrainEffect ****************

// ********** Begin Class AAURACRONPCGChaosIsland Function GrantHighRiskReward *********************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics
{
	struct AURACRONPCGChaosIsland_eventGrantHighRiskReward_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Concede recompensa de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Concede recompensa de alto risco" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventGrantHighRiskReward_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "GrantHighRiskReward", Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::AURACRONPCGChaosIsland_eventGrantHighRiskReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::AURACRONPCGChaosIsland_eventGrantHighRiskReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execGrantHighRiskReward)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GrantHighRiskReward(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function GrantHighRiskReward ***********************

// ********** Begin Class AAURACRONPCGChaosIsland Function MulticastPlayChaosEffectVFX *************
struct AURACRONPCGChaosIsland_eventMulticastPlayChaosEffectVFX_Parms
{
	FVector Location;
};
static FName NAME_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX = FName(TEXT("MulticastPlayChaosEffectVFX"));
void AAURACRONPCGChaosIsland::MulticastPlayChaosEffectVFX(FVector const& Location)
{
	AURACRONPCGChaosIsland_eventMulticastPlayChaosEffectVFX_Parms Parms;
	Parms.Location=Location;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// RPC para reproduzir efeitos visuais em todos os clientes\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para reproduzir efeitos visuais em todos os clientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventMulticastPlayChaosEffectVFX_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "MulticastPlayChaosEffectVFX", Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::PropPointers), sizeof(AURACRONPCGChaosIsland_eventMulticastPlayChaosEffectVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00844CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGChaosIsland_eventMulticastPlayChaosEffectVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execMulticastPlayChaosEffectVFX)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastPlayChaosEffectVFX_Implementation(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function MulticastPlayChaosEffectVFX ***************

// ********** Begin Class AAURACRONPCGChaosIsland Function OnChaosRemovalVFXLoaded *****************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics
{
	struct AURACRONPCGChaosIsland_eventOnChaosRemovalVFXLoaded_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventOnChaosRemovalVFXLoaded_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "OnChaosRemovalVFXLoaded", Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnChaosRemovalVFXLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnChaosRemovalVFXLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execOnChaosRemovalVFXLoaded)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnChaosRemovalVFXLoaded(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function OnChaosRemovalVFXLoaded *******************

// ********** Begin Class AAURACRONPCGChaosIsland Function OnChaosVFXLoaded ************************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics
{
	struct AURACRONPCGChaosIsland_eventOnChaosVFXLoaded_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Callbacks para carregamento ass\xc3\xadncrono\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callbacks para carregamento ass\xc3\xadncrono" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventOnChaosVFXLoaded_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "OnChaosVFXLoaded", Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnChaosVFXLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnChaosVFXLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execOnChaosVFXLoaded)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnChaosVFXLoaded(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function OnChaosVFXLoaded **************************

// ********** Begin Class AAURACRONPCGChaosIsland Function OnEnvironmentalHazardPeriodicVFXLoaded **
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics
{
	struct AURACRONPCGChaosIsland_eventOnEnvironmentalHazardPeriodicVFXLoaded_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventOnEnvironmentalHazardPeriodicVFXLoaded_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "OnEnvironmentalHazardPeriodicVFXLoaded", Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnEnvironmentalHazardPeriodicVFXLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnEnvironmentalHazardPeriodicVFXLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execOnEnvironmentalHazardPeriodicVFXLoaded)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnEnvironmentalHazardPeriodicVFXLoaded(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function OnEnvironmentalHazardPeriodicVFXLoaded ****

// ********** Begin Class AAURACRONPCGChaosIsland Function OnEnvironmentalHazardVFXLoaded **********
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics
{
	struct AURACRONPCGChaosIsland_eventOnEnvironmentalHazardVFXLoaded_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventOnEnvironmentalHazardVFXLoaded_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "OnEnvironmentalHazardVFXLoaded", Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnEnvironmentalHazardVFXLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnEnvironmentalHazardVFXLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execOnEnvironmentalHazardVFXLoaded)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnEnvironmentalHazardVFXLoaded(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function OnEnvironmentalHazardVFXLoaded ************

// ********** Begin Class AAURACRONPCGChaosIsland Function OnHighRiskRewardSoundLoaded *************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics
{
	struct AURACRONPCGChaosIsland_eventOnHighRiskRewardSoundLoaded_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventOnHighRiskRewardSoundLoaded_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "OnHighRiskRewardSoundLoaded", Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::AURACRONPCGChaosIsland_eventOnHighRiskRewardSoundLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::AURACRONPCGChaosIsland_eventOnHighRiskRewardSoundLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execOnHighRiskRewardSoundLoaded)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHighRiskRewardSoundLoaded(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function OnHighRiskRewardSoundLoaded ***************

// ********** Begin Class AAURACRONPCGChaosIsland Function OnHighRiskRewardVFXLoaded ***************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics
{
	struct AURACRONPCGChaosIsland_eventOnHighRiskRewardVFXLoaded_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventOnHighRiskRewardVFXLoaded_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "OnHighRiskRewardVFXLoaded", Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnHighRiskRewardVFXLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnHighRiskRewardVFXLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execOnHighRiskRewardVFXLoaded)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHighRiskRewardVFXLoaded(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function OnHighRiskRewardVFXLoaded *****************

// ********** Begin Class AAURACRONPCGChaosIsland Function OnUnstableTerrainVFXLoaded **************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics
{
	struct AURACRONPCGChaosIsland_eventOnUnstableTerrainVFXLoaded_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventOnUnstableTerrainVFXLoaded_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "OnUnstableTerrainVFXLoaded", Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnUnstableTerrainVFXLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00840401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::AURACRONPCGChaosIsland_eventOnUnstableTerrainVFXLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execOnUnstableTerrainVFXLoaded)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnUnstableTerrainVFXLoaded(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function OnUnstableTerrainVFXLoaded ****************

// ********** Begin Class AAURACRONPCGChaosIsland Function RemoveChaosEffects **********************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics
{
	struct AURACRONPCGChaosIsland_eventRemoveChaosEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove os efeitos ca\xc3\xb3ticos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove os efeitos ca\xc3\xb3ticos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventRemoveChaosEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "RemoveChaosEffects", Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::AURACRONPCGChaosIsland_eventRemoveChaosEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::AURACRONPCGChaosIsland_eventRemoveChaosEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execRemoveChaosEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveChaosEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function RemoveChaosEffects ************************

// ********** Begin Class AAURACRONPCGChaosIsland Function RemoveEnvironmentalHazardEffects ********
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics
{
	struct AURACRONPCGChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove efeitos de perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove efeitos de perigos ambientais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "RemoveEnvironmentalHazardEffects", Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::AURACRONPCGChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::AURACRONPCGChaosIsland_eventRemoveEnvironmentalHazardEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execRemoveEnvironmentalHazardEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveEnvironmentalHazardEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function RemoveEnvironmentalHazardEffects **********

// ********** Begin Class AAURACRONPCGChaosIsland Function RemoveUnstableTerrainEffects ************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics
{
	struct AURACRONPCGChaosIsland_eventRemoveUnstableTerrainEffects_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove efeitos de terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove efeitos de terreno inst\xc3\xa1vel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventRemoveUnstableTerrainEffects_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "RemoveUnstableTerrainEffects", Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::AURACRONPCGChaosIsland_eventRemoveUnstableTerrainEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::AURACRONPCGChaosIsland_eventRemoveUnstableTerrainEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execRemoveUnstableTerrainEffects)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveUnstableTerrainEffects(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function RemoveUnstableTerrainEffects **************

// ********** Begin Class AAURACRONPCGChaosIsland Function ServerApplyChaosEffect ******************
struct AURACRONPCGChaosIsland_eventServerApplyChaosEffect_Parms
{
	AActor* TargetActor;
};
static FName NAME_AAURACRONPCGChaosIsland_ServerApplyChaosEffect = FName(TEXT("ServerApplyChaosEffect"));
void AAURACRONPCGChaosIsland::ServerApplyChaosEffect(AActor* TargetActor)
{
	AURACRONPCGChaosIsland_eventServerApplyChaosEffect_Parms Parms;
	Parms.TargetActor=TargetActor;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGChaosIsland_ServerApplyChaosEffect);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// RPC para aplicar efeito ca\xc3\xb3tico no servidor\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para aplicar efeito ca\xc3\xb3tico no servidor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventServerApplyChaosEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "ServerApplyChaosEffect", Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::PropPointers), sizeof(AURACRONPCGChaosIsland_eventServerApplyChaosEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGChaosIsland_eventServerApplyChaosEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execServerApplyChaosEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerApplyChaosEffect_Validate(Z_Param_TargetActor))
	{
		RPC_ValidateFailed(TEXT("ServerApplyChaosEffect_Validate"));
		return;
	}
	P_THIS->ServerApplyChaosEffect_Implementation(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function ServerApplyChaosEffect ********************

// ********** Begin Class AAURACRONPCGChaosIsland Function ServerUpdateActivityLevel ***************
struct AURACRONPCGChaosIsland_eventServerUpdateActivityLevel_Parms
{
	float NewActivityLevel;
};
static FName NAME_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel = FName(TEXT("ServerUpdateActivityLevel"));
void AAURACRONPCGChaosIsland::ServerUpdateActivityLevel(float NewActivityLevel)
{
	AURACRONPCGChaosIsland_eventServerUpdateActivityLevel_Parms Parms;
	Parms.NewActivityLevel=NewActivityLevel;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// RPC para atualizar n\xc3\xadvel de atividade no servidor\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para atualizar n\xc3\xadvel de atividade no servidor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewActivityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::NewProp_NewActivityLevel = { "NewActivityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventServerUpdateActivityLevel_Parms, NewActivityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::NewProp_NewActivityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "ServerUpdateActivityLevel", Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::PropPointers), sizeof(AURACRONPCGChaosIsland_eventServerUpdateActivityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x80240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGChaosIsland_eventServerUpdateActivityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execServerUpdateActivityLevel)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewActivityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerUpdateActivityLevel_Validate(Z_Param_NewActivityLevel))
	{
		RPC_ValidateFailed(TEXT("ServerUpdateActivityLevel_Validate"));
		return;
	}
	P_THIS->ServerUpdateActivityLevel_Implementation(Z_Param_NewActivityLevel);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function ServerUpdateActivityLevel *****************

// ********** Begin Class AAURACRONPCGChaosIsland Function SetActivityLevel ************************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics
{
	struct AURACRONPCGChaosIsland_eventSetActivityLevel_Parms
	{
		float NewActivityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir n\xc3\xadvel de atividade da ilha (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir n\xc3\xadvel de atividade da ilha (0.0 - 1.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewActivityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::NewProp_NewActivityLevel = { "NewActivityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventSetActivityLevel_Parms, NewActivityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::NewProp_NewActivityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "SetActivityLevel", Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::AURACRONPCGChaosIsland_eventSetActivityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::AURACRONPCGChaosIsland_eventSetActivityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execSetActivityLevel)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewActivityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityLevel(Z_Param_NewActivityLevel);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function SetActivityLevel **************************

// ********** Begin Class AAURACRONPCGChaosIsland Function UpdateEnvironmentalHazards **************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics
{
	struct AURACRONPCGChaosIsland_eventUpdateEnvironmentalHazards_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualiza os perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza os perigos ambientais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventUpdateEnvironmentalHazards_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "UpdateEnvironmentalHazards", Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::AURACRONPCGChaosIsland_eventUpdateEnvironmentalHazards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::AURACRONPCGChaosIsland_eventUpdateEnvironmentalHazards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execUpdateEnvironmentalHazards)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateEnvironmentalHazards(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function UpdateEnvironmentalHazards ****************

// ********** Begin Class AAURACRONPCGChaosIsland Function UpdateUnstableTerrain *******************
struct Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics
{
	struct AURACRONPCGChaosIsland_eventUpdateUnstableTerrain_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualiza o estado do terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza o estado do terreno inst\xc3\xa1vel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIsland_eventUpdateUnstableTerrain_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIsland, nullptr, "UpdateUnstableTerrain", Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::AURACRONPCGChaosIsland_eventUpdateUnstableTerrain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::AURACRONPCGChaosIsland_eventUpdateUnstableTerrain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIsland::execUpdateUnstableTerrain)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateUnstableTerrain(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIsland Function UpdateUnstableTerrain *********************

// ********** Begin Class AAURACRONPCGChaosIsland **************************************************
void AAURACRONPCGChaosIsland::StaticRegisterNativesAAURACRONPCGChaosIsland()
{
	UClass* Class = AAURACRONPCGChaosIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyChaosEffect", &AAURACRONPCGChaosIsland::execApplyChaosEffect },
		{ "ApplyEnvironmentalHazardEffect", &AAURACRONPCGChaosIsland::execApplyEnvironmentalHazardEffect },
		{ "ApplyUnstableTerrainEffect", &AAURACRONPCGChaosIsland::execApplyUnstableTerrainEffect },
		{ "GrantHighRiskReward", &AAURACRONPCGChaosIsland::execGrantHighRiskReward },
		{ "MulticastPlayChaosEffectVFX", &AAURACRONPCGChaosIsland::execMulticastPlayChaosEffectVFX },
		{ "OnChaosRemovalVFXLoaded", &AAURACRONPCGChaosIsland::execOnChaosRemovalVFXLoaded },
		{ "OnChaosVFXLoaded", &AAURACRONPCGChaosIsland::execOnChaosVFXLoaded },
		{ "OnEnvironmentalHazardPeriodicVFXLoaded", &AAURACRONPCGChaosIsland::execOnEnvironmentalHazardPeriodicVFXLoaded },
		{ "OnEnvironmentalHazardVFXLoaded", &AAURACRONPCGChaosIsland::execOnEnvironmentalHazardVFXLoaded },
		{ "OnHighRiskRewardSoundLoaded", &AAURACRONPCGChaosIsland::execOnHighRiskRewardSoundLoaded },
		{ "OnHighRiskRewardVFXLoaded", &AAURACRONPCGChaosIsland::execOnHighRiskRewardVFXLoaded },
		{ "OnUnstableTerrainVFXLoaded", &AAURACRONPCGChaosIsland::execOnUnstableTerrainVFXLoaded },
		{ "RemoveChaosEffects", &AAURACRONPCGChaosIsland::execRemoveChaosEffects },
		{ "RemoveEnvironmentalHazardEffects", &AAURACRONPCGChaosIsland::execRemoveEnvironmentalHazardEffects },
		{ "RemoveUnstableTerrainEffects", &AAURACRONPCGChaosIsland::execRemoveUnstableTerrainEffects },
		{ "ServerApplyChaosEffect", &AAURACRONPCGChaosIsland::execServerApplyChaosEffect },
		{ "ServerUpdateActivityLevel", &AAURACRONPCGChaosIsland::execServerUpdateActivityLevel },
		{ "SetActivityLevel", &AAURACRONPCGChaosIsland::execSetActivityLevel },
		{ "UpdateEnvironmentalHazards", &AAURACRONPCGChaosIsland::execUpdateEnvironmentalHazards },
		{ "UpdateUnstableTerrain", &AAURACRONPCGChaosIsland::execUpdateUnstableTerrain },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGChaosIsland;
UClass* AAURACRONPCGChaosIsland::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGChaosIsland;
	if (!Z_Registration_Info_UClass_AAURACRONPCGChaosIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGChaosIsland"),
			Z_Registration_Info_UClass_AAURACRONPCGChaosIsland.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGChaosIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGChaosIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister()
{
	return AAURACRONPCGChaosIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Chaos Island\n * Ilha com elementos ca\xc3\xb3ticos, v\xc3\xb3rtices de energia e runas antigas\n *\n * Caracter\xc3\xadsticas conforme GDD:\n * - Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n * - Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n * - Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGChaosIsland.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementa\xc3\xa7\xc3\xa3o espec\xc3\xad""fica da Chaos Island\nIlha com elementos ca\xc3\xb3ticos, v\xc3\xb3rtices de energia e runas antigas\n\nCaracter\xc3\xadsticas conforme GDD:\n- Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n- Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n- Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazardIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade dos perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade dos perigos ambientais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazardDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o dos perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o dos perigos ambientais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardMultiplier_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multiplicador de recompensas de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o das recompensas de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o das recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainInstabilityIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade da instabilidade do terreno\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da instabilidade do terreno" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainInstabilityDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o da instabilidade do terreno\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o da instabilidade do terreno" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VortexRotationSpeed_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Velocidade de rota\xc3\xa7\xc3\xa3o dos v\xc3\xb3rtices\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de rota\xc3\xa7\xc3\xa3o dos v\xc3\xb3rtices" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RunePulseIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade de pulsa\xc3\xa7\xc3\xa3o das runas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade de pulsa\xc3\xa7\xc3\xa3o das runas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionEnvironments_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ambientes de transi\xc3\xa7\xc3\xa3o para efeitos visuais adaptativos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambientes de transi\xc3\xa7\xc3\xa3o para efeitos visuais adaptativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosSpire_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Espiral central do caos\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Espiral central do caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyVortexes_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// V\xc3\xb3rtices de energia\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "V\xc3\xb3rtices de energia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AncientRunes_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Runas antigas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Runas antigas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HazardZones_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Zonas de perigo ambiental\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Zonas de perigo ambiental" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazards_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Perigos ambientais (alias para compatibilidade)\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perigos ambientais (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnstableTerrainZones_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes de terreno inst\xc3\xa1vel\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de terreno inst\xc3\xa1vel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardZones_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes de recompensas de alto risco\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosIntensity_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intensidade do efeito ca\xc3\xb3tico\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito ca\xc3\xb3tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosDuration_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dura\xc3\xa7\xc3\xa3o do efeito ca\xc3\xb3tico\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito ca\xc3\xb3tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosVisualEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito visual do caos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito visual do caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosGameplayEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para caos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalHazardEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para perigos ambientais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para perigos ambientais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnstableTerrainEffect_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Efeito de gameplay para terreno inst\xc3\xa1vel\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de gameplay para terreno inst\xc3\xa1vel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighRiskRewardsTable_MetaData[] = {
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tabela de recompensas de alto risco\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tabela de recompensas de alto risco" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo acumulado para efeitos visuais\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityLevel_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de atividade atual da ilha (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de atividade atual da ilha (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosEnergyEffect_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeito de energia ca\xc3\xb3tica */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeito de energia ca\xc3\xb3tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosLight_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Prismal Flow|Chaos Island" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de luz ca\xc3\xb3tica */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de luz ca\xc3\xb3tica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentalHazardIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentalHazardDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HighRiskRewardMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HighRiskRewardDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainInstabilityIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainInstabilityDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VortexRotationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RunePulseIntensity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionEnvironments_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionEnvironments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TransitionEnvironments;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosSpire;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnergyVortexes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnergyVortexes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AncientRunes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AncientRunes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HazardZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HazardZones;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentalHazards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnvironmentalHazards;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UnstableTerrainZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnstableTerrainZones;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HighRiskRewardZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HighRiskRewardZones;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChaosIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChaosDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosVisualEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ChaosGameplayEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_EnvironmentalHazardEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_UnstableTerrainEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HighRiskRewardsTable;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityLevel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosEnergyEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosLight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyChaosEffect, "ApplyChaosEffect" }, // 3952509563
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyEnvironmentalHazardEffect, "ApplyEnvironmentalHazardEffect" }, // 3710574214
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_ApplyUnstableTerrainEffect, "ApplyUnstableTerrainEffect" }, // 1676659280
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_GrantHighRiskReward, "GrantHighRiskReward" }, // 2307711868
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_MulticastPlayChaosEffectVFX, "MulticastPlayChaosEffectVFX" }, // 4261476202
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosRemovalVFXLoaded, "OnChaosRemovalVFXLoaded" }, // 3189546272
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnChaosVFXLoaded, "OnChaosVFXLoaded" }, // 3350307767
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardPeriodicVFXLoaded, "OnEnvironmentalHazardPeriodicVFXLoaded" }, // 4074569051
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnEnvironmentalHazardVFXLoaded, "OnEnvironmentalHazardVFXLoaded" }, // 1050640303
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardSoundLoaded, "OnHighRiskRewardSoundLoaded" }, // 2860990217
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnHighRiskRewardVFXLoaded, "OnHighRiskRewardVFXLoaded" }, // 4107744423
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_OnUnstableTerrainVFXLoaded, "OnUnstableTerrainVFXLoaded" }, // 1812248402
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveChaosEffects, "RemoveChaosEffects" }, // 3396127715
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveEnvironmentalHazardEffects, "RemoveEnvironmentalHazardEffects" }, // 4106269313
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_RemoveUnstableTerrainEffects, "RemoveUnstableTerrainEffects" }, // 1101038894
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerApplyChaosEffect, "ServerApplyChaosEffect" }, // 1098668780
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_ServerUpdateActivityLevel, "ServerUpdateActivityLevel" }, // 3033800531
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_SetActivityLevel, "SetActivityLevel" }, // 1977218216
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateEnvironmentalHazards, "UpdateEnvironmentalHazards" }, // 3146883162
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIsland_UpdateUnstableTerrain, "UpdateUnstableTerrain" }, // 1297756404
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGChaosIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazardIntensity = { "EnvironmentalHazardIntensity", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, EnvironmentalHazardIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazardIntensity_MetaData), NewProp_EnvironmentalHazardIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazardDuration = { "EnvironmentalHazardDuration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, EnvironmentalHazardDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazardDuration_MetaData), NewProp_EnvironmentalHazardDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardMultiplier = { "HighRiskRewardMultiplier", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, HighRiskRewardMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardMultiplier_MetaData), NewProp_HighRiskRewardMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardDuration = { "HighRiskRewardDuration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, HighRiskRewardDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardDuration_MetaData), NewProp_HighRiskRewardDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TerrainInstabilityIntensity = { "TerrainInstabilityIntensity", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, TerrainInstabilityIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainInstabilityIntensity_MetaData), NewProp_TerrainInstabilityIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TerrainInstabilityDuration = { "TerrainInstabilityDuration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, TerrainInstabilityDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainInstabilityDuration_MetaData), NewProp_TerrainInstabilityDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_VortexRotationSpeed = { "VortexRotationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, VortexRotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VortexRotationSpeed_MetaData), NewProp_VortexRotationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_RunePulseIntensity = { "RunePulseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, RunePulseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RunePulseIntensity_MetaData), NewProp_RunePulseIntensity_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TransitionEnvironments_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TransitionEnvironments_Inner = { "TransitionEnvironments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TransitionEnvironments = { "TransitionEnvironments", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, TransitionEnvironments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionEnvironments_MetaData), NewProp_TransitionEnvironments_MetaData) }; // 2509470107
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosSpire = { "ChaosSpire", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ChaosSpire), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosSpire_MetaData), NewProp_ChaosSpire_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnergyVortexes_Inner = { "EnergyVortexes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnergyVortexes = { "EnergyVortexes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, EnergyVortexes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyVortexes_MetaData), NewProp_EnergyVortexes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_AncientRunes_Inner = { "AncientRunes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_AncientRunes = { "AncientRunes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, AncientRunes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AncientRunes_MetaData), NewProp_AncientRunes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HazardZones_Inner = { "HazardZones", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HazardZones = { "HazardZones", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, HazardZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HazardZones_MetaData), NewProp_HazardZones_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazards_Inner = { "EnvironmentalHazards", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazards = { "EnvironmentalHazards", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, EnvironmentalHazards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazards_MetaData), NewProp_EnvironmentalHazards_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_UnstableTerrainZones_Inner = { "UnstableTerrainZones", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_UnstableTerrainZones = { "UnstableTerrainZones", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, UnstableTerrainZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnstableTerrainZones_MetaData), NewProp_UnstableTerrainZones_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardZones_Inner = { "HighRiskRewardZones", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardZones = { "HighRiskRewardZones", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, HighRiskRewardZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardZones_MetaData), NewProp_HighRiskRewardZones_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosIntensity = { "ChaosIntensity", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ChaosIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosIntensity_MetaData), NewProp_ChaosIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosDuration = { "ChaosDuration", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ChaosDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosDuration_MetaData), NewProp_ChaosDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosVisualEffect = { "ChaosVisualEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ChaosVisualEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosVisualEffect_MetaData), NewProp_ChaosVisualEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosGameplayEffect = { "ChaosGameplayEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ChaosGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosGameplayEffect_MetaData), NewProp_ChaosGameplayEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazardEffect = { "EnvironmentalHazardEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, EnvironmentalHazardEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalHazardEffect_MetaData), NewProp_EnvironmentalHazardEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_UnstableTerrainEffect = { "UnstableTerrainEffect", nullptr, (EPropertyFlags)0x0024080000000025, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, UnstableTerrainEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnstableTerrainEffect_MetaData), NewProp_UnstableTerrainEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardsTable = { "HighRiskRewardsTable", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, HighRiskRewardsTable), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighRiskRewardsTable_MetaData), NewProp_HighRiskRewardsTable_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ActivityLevel = { "ActivityLevel", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ActivityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityLevel_MetaData), NewProp_ActivityLevel_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosEnergyEffect = { "ChaosEnergyEffect", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ChaosEnergyEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosEnergyEffect_MetaData), NewProp_ChaosEnergyEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosLight = { "ChaosLight", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIsland, ChaosLight), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosLight_MetaData), NewProp_ChaosLight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazardIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazardDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TerrainInstabilityIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TerrainInstabilityDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_VortexRotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_RunePulseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TransitionEnvironments_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TransitionEnvironments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_TransitionEnvironments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosSpire,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnergyVortexes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnergyVortexes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_AncientRunes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_AncientRunes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HazardZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HazardZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_UnstableTerrainZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_UnstableTerrainZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosVisualEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_EnvironmentalHazardEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_UnstableTerrainEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_HighRiskRewardsTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_AccumulatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ActivityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosEnergyEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::NewProp_ChaosLight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APrismalFlowIsland,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::ClassParams = {
	&AAURACRONPCGChaosIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGChaosIsland()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGChaosIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGChaosIsland.OuterSingleton, Z_Construct_UClass_AAURACRONPCGChaosIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGChaosIsland.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGChaosIsland::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_EnvironmentalHazardIntensity(TEXT("EnvironmentalHazardIntensity"));
	static FName Name_EnvironmentalHazardDuration(TEXT("EnvironmentalHazardDuration"));
	static FName Name_HighRiskRewardMultiplier(TEXT("HighRiskRewardMultiplier"));
	static FName Name_HighRiskRewardDuration(TEXT("HighRiskRewardDuration"));
	static FName Name_TerrainInstabilityIntensity(TEXT("TerrainInstabilityIntensity"));
	static FName Name_TerrainInstabilityDuration(TEXT("TerrainInstabilityDuration"));
	static FName Name_TransitionEnvironments(TEXT("TransitionEnvironments"));
	static FName Name_ChaosIntensity(TEXT("ChaosIntensity"));
	static FName Name_ChaosDuration(TEXT("ChaosDuration"));
	static FName Name_ChaosGameplayEffect(TEXT("ChaosGameplayEffect"));
	static FName Name_EnvironmentalHazardEffect(TEXT("EnvironmentalHazardEffect"));
	static FName Name_UnstableTerrainEffect(TEXT("UnstableTerrainEffect"));
	const bool bIsValid = true
		&& Name_EnvironmentalHazardIntensity == ClassReps[(int32)ENetFields_Private::EnvironmentalHazardIntensity].Property->GetFName()
		&& Name_EnvironmentalHazardDuration == ClassReps[(int32)ENetFields_Private::EnvironmentalHazardDuration].Property->GetFName()
		&& Name_HighRiskRewardMultiplier == ClassReps[(int32)ENetFields_Private::HighRiskRewardMultiplier].Property->GetFName()
		&& Name_HighRiskRewardDuration == ClassReps[(int32)ENetFields_Private::HighRiskRewardDuration].Property->GetFName()
		&& Name_TerrainInstabilityIntensity == ClassReps[(int32)ENetFields_Private::TerrainInstabilityIntensity].Property->GetFName()
		&& Name_TerrainInstabilityDuration == ClassReps[(int32)ENetFields_Private::TerrainInstabilityDuration].Property->GetFName()
		&& Name_TransitionEnvironments == ClassReps[(int32)ENetFields_Private::TransitionEnvironments].Property->GetFName()
		&& Name_ChaosIntensity == ClassReps[(int32)ENetFields_Private::ChaosIntensity].Property->GetFName()
		&& Name_ChaosDuration == ClassReps[(int32)ENetFields_Private::ChaosDuration].Property->GetFName()
		&& Name_ChaosGameplayEffect == ClassReps[(int32)ENetFields_Private::ChaosGameplayEffect].Property->GetFName()
		&& Name_EnvironmentalHazardEffect == ClassReps[(int32)ENetFields_Private::EnvironmentalHazardEffect].Property->GetFName()
		&& Name_UnstableTerrainEffect == ClassReps[(int32)ENetFields_Private::UnstableTerrainEffect].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGChaosIsland"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGChaosIsland);
AAURACRONPCGChaosIsland::~AAURACRONPCGChaosIsland() {}
// ********** End Class AAURACRONPCGChaosIsland ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGChaosIsland, AAURACRONPCGChaosIsland::StaticClass, TEXT("AAURACRONPCGChaosIsland"), &Z_Registration_Info_UClass_AAURACRONPCGChaosIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGChaosIsland), 864282117U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_418158053(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIsland_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
