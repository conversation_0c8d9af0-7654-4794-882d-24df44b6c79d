// AURACRONPCGEnvironment.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Classe para gerenciar os ambientes procedurais

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGTypes.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/PostProcessComponent.h"
#include "Engine/PostProcessVolume.h"
#include "AURACRONPCGEnvironment.generated.h"

class UPCGComponent;
class AStaticMeshActor;

/**
 * Estrutura para armazenar informações de uma floresta respirante
 */
USTRUCT()
struct FBreathingForestData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Center;

    UPROPERTY()
    float Radius;

    UPROPERTY()
    TArray<FVector> TreePositions;

    UPROPERTY()
    AStaticMeshActor* TreeActor = nullptr;

    UPROPERTY()
    float BreathSpeed = 1.0f;

    UPROPERTY()
    float OriginalScale = 1.0f;

    UPROPERTY()
    float BreathAmount = 0.1f;

    FBreathingForestData()
        : Center(FVector::ZeroVector)
        , Radius(0.0f)
    {
    }

    FBreathingForestData(const FVector& InCenter, float InRadius, const TArray<FVector>& InTreePositions)
        : Center(InCenter)
        , Radius(InRadius)
        , TreePositions(InTreePositions)
    {
    }
};

/**
 * Estrutura para armazenar dados de ilhas flutuantes
 */
USTRUCT()
struct FFloatingIslandData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* IslandActor = nullptr;

    UPROPERTY()
    float OriginalHeight = 0.0f;

    UPROPERTY()
    float FloatHeight = 10.0f;

    UPROPERTY()
    float FloatSpeed = 1.0f;

    UPROPERTY()
    float TimeOffset = 0.0f;
};

/**
 * Estrutura para armazenar dados das fortalezas de nuvem
 */
USTRUCT()
struct FCloudFortressData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* FortressActor = nullptr;

    UPROPERTY()
    FVector OriginalPosition = FVector::ZeroVector;

    UPROPERTY()
    FVector DriftDirection = FVector::ZeroVector;

    UPROPERTY()
    float DriftSpeed = 1.0f;

    UPROPERTY()
    float HorizontalSpeed = 0.5f;

    UPROPERTY()
    float VerticalSpeed = 0.5f;

    UPROPERTY()
    float DriftRadius = 200.0f;

    UPROPERTY()
    float TimeOffset = 0.0f;

    UPROPERTY()
    float VerticalAmplitude = 50.0f;

    // Campos adicionais para compatibilidade com FFloatingIslandData
    UPROPERTY()
    AStaticMeshActor* IslandActor = nullptr;

    UPROPERTY()
    float OriginalHeight = 0.0f;

    UPROPERTY()
    float FloatHeight = 50.0f;

    UPROPERTY()
    float FloatSpeed = 0.5f;
};

/**
 * Estrutura para armazenar dados dos jardins estelares
 */
USTRUCT()
struct FStellarGardenData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* GardenActor = nullptr;

    UPROPERTY()
    float GravityStrength = 0.5f;

    UPROPERTY()
    float ResourceDensity = 1.0f;

    UPROPERTY()
    float TimeOffset = 0.0f;

    UPROPERTY()
    float RotationSpeed = 1.0f;

    UPROPERTY()
    float PulseSpeed = 2.0f;

    // Campos adicionais necessários
    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float PulsationAmplitude = 0.2f;

    UPROPERTY()
    float PulsationSpeed = 1.0f;
};

/**
 * Estrutura para armazenar dados das pontes aurora
 */
USTRUCT()
struct FAuroraBridgeData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* BridgeActor = nullptr;

    UPROPERTY()
    bool bIsVisible = false;

    UPROPERTY()
    float VisibilityTimer = 0.0f;

    UPROPERTY()
    float VisibilityDuration = 30.0f;

    UPROPERTY()
    float InvisibilityDuration = 60.0f;

    UPROPERTY()
    UNiagaraComponent* ParticleComponent = nullptr;
};

/**
 * Estrutura para armazenar dados dos rios de almas
 */
USTRUCT()
struct FRiverOfSoulsData
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<FVector> FlowPoints;

    UPROPERTY()
    FVector FlowDirection = FVector::ZeroVector;

    UPROPERTY()
    float FlowSpeed = 1.0f;

    UPROPERTY()
    float Width = 500.0f;

    UPROPERTY()
    UNiagaraComponent* ParticleComponent = nullptr;

    UPROPERTY()
    AStaticMeshActor* RiverActor = nullptr;

    UPROPERTY()
    float BaseFlowSpeed = 1.0f;

    UPROPERTY()
    float BaseSoulDensity = 1.0f;
};

/**
 * Estrutura para armazenar dados das estruturas fragmentadas
 */
USTRUCT()
struct FFragmentedStructureData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* StructureActor = nullptr;

    UPROPERTY()
    FVector OriginalPosition = FVector::ZeroVector;

    UPROPERTY()
    float FragmentationLevel = 0.5f;

    UPROPERTY()
    float TimeOffset = 0.0f;

    UPROPERTY()
    float FloatSpeed = 1.0f;

    UPROPERTY()
    float FloatHeight = 50.0f;

    UPROPERTY()
    float OriginalHeight = 0.0f;

    UPROPERTY()
    FVector RotationFactor = FVector(1.0f, 1.0f, 1.0f);

    UPROPERTY()
    float RotationSpeed = 1.0f;
};

/**
 * Estrutura para armazenar dados das zonas de distorção temporal
 */
USTRUCT()
struct FTemporalDistortionData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Center = FVector::ZeroVector;

    UPROPERTY()
    float Radius = 1000.0f;

    UPROPERTY()
    float TimeScale = 0.5f;

    UPROPERTY()
    UNiagaraComponent* ParticleComponent = nullptr;

    UPROPERTY()
    AStaticMeshActor* DistortionActor = nullptr;

    UPROPERTY()
    float PulseSpeed = 1.0f;
};

/**
 * Estrutura para armazenar dados dos nexos sombrios
 */
USTRUCT()
struct FShadowNexusData
{
    GENERATED_BODY()

    UPROPERTY()
    AStaticMeshActor* NexusActor = nullptr;

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float ControlRadius = 2000.0f;

    UPROPERTY()
    float EnergyLevel = 1.0f;

    UPROPERTY()
    bool bIsActive = true;

    UPROPERTY()
    TArray<AActor*> ConnectedStructures;

    // Propriedades adicionais para o sistema de Shadow Nexuses
    UPROPERTY()
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY()
    FVector Scale = FVector::OneVector;

    UPROPERTY()
    float Energy = 1.0f;

    UPROPERTY()
    class UNiagaraComponent* ParticleEffect = nullptr;

    UPROPERTY()
    float PulseRate = 1.0f;

    UPROPERTY()
    float TimeOffset = 0.0f;

    UPROPERTY()
    int32 NexusType = 0;
};

/**
 * Estrutura para armazenar dados das torres de lamentação
 */
USTRUCT()
struct FTowerOfLamentationData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float DrainRadius = 1500.0f;

    UPROPERTY()
    float DrainStrength = 0.1f;

    UPROPERTY()
    float EnergyCapacity = 100.0f;

    UPROPERTY()
    float CurrentEnergy = 0.0f;

    UPROPERTY()
    AStaticMeshActor* TowerActor = nullptr;

    // Campos adicionais necessários para implementação completa
    UPROPERTY()
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY()
    FVector Scale = FVector::OneVector;

    UPROPERTY()
    float Energy = 1.0f;

    UPROPERTY()
    float PulseRate = 1.0f;

    UPROPERTY()
    float TimeOffset = 0.0f;

    UPROPERTY()
    int32 TowerType = 0;

    UPROPERTY()
    class UNiagaraComponent* ParticleEffect = nullptr;
};

/**
 * Estrutura para armazenar dados da Âncora do Purgatório
 */
USTRUCT()
struct FPurgatoryAnchorData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float AnchorStrength = 1.0f;

    UPROPERTY()
    float InfluenceRadius = 3000.0f;

    UPROPERTY()
    bool bIsStabilized = false;

    UPROPERTY()
    float StabilityLevel = 0.5f;

    UPROPERTY()
    TArray<FVector> ConnectedPoints;

    UPROPERTY()
    float EnergyOutput = 1.0f;

    UPROPERTY()
    float ResonanceFrequency = 1.0f;

    UPROPERTY()
    bool bHasSpectralGuardian = false;

    UPROPERTY()
    AStaticMeshActor* AnchorActor = nullptr;

    UPROPERTY()
    UNiagaraComponent* EnergyEffect = nullptr;

    UPROPERTY()
    UNiagaraComponent* ResonanceEffect = nullptr;

    UPROPERTY()
    TArray<UNiagaraComponent*> ConnectionEffects;

    UPROPERTY()
    float Radius = 300.0f;

    UPROPERTY()
    float RotationSpeed = 15.0f;

    UPROPERTY()
    float PulsationAmplitude = 0.3f;

    UPROPERTY()
    float PulsationSpeed = 1.2f;

    UPROPERTY()
    bool bIsActive = false;

    UPROPERTY()
    UNiagaraComponent* AuraComponent = nullptr;
};

/**
 * Estrutura para armazenar dados do Guardião Espectral
 */
USTRUCT()
struct FSpectralGuardianData
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Position = FVector::ZeroVector;

    UPROPERTY()
    float Health = 100.0f;

    UPROPERTY()
    float MaxHealth = 100.0f;

    UPROPERTY()
    bool bIsActive = false;

    UPROPERTY()
    float SpawnTimer = 0.0f;

    UPROPERTY()
    float RespawnTime = 300.0f;

    UPROPERTY()
    TArray<FVector> PatrolPoints;

    UPROPERTY()
    int32 CurrentPatrolIndex = 0;

    UPROPERTY()
    float MovementSpeed = 600.0f;

    UPROPERTY()
    float AttackRange = 1200.0f;

    UPROPERTY()
    float AttackDamage = 50.0f;

    UPROPERTY()
    float AttackCooldown = 2.0f;

    UPROPERTY()
    float LastAttackTime = 0.0f;

    // Propriedades que estavam faltando
    UPROPERTY()
    float RotationSpeed = 0.2f;

    UPROPERTY()
    float PulsationAmplitude = 0.3f;

    UPROPERTY()
    float PulsationSpeed = 0.25f;

    UPROPERTY()
    float Radius = 500.0f;

    UPROPERTY()
    bool IsActive = false;

    UPROPERTY()
    AActor* TargetActor = nullptr;

    UPROPERTY()
    AStaticMeshActor* GuardianActor = nullptr;

    UPROPERTY()
    UNiagaraComponent* SpectralEffect = nullptr;

    UPROPERTY()
    UNiagaraComponent* AuraComponent = nullptr;
};





/**
 * Ator individual para gerenciar um ambiente procedural específico no AURACRON
 *
 * RESPONSABILIDADES CLARIFICADAS:
 * ================================
 *
 * ESTA CLASSE (AURACRONPCGEnvironment) - ATOR INDIVIDUAL:
 * - Representa UMA INSTÂNCIA específica de ambiente (ex: uma área de Radiant Plains)
 * - Gerencia a geração PCG local de elementos específicos (árvores, rochas, estruturas)
 * - Controla características visuais e físicas do ambiente local
 * - Responde a comandos do EnvironmentManager para ativação/desativação
 * - Implementa lógica específica de cada tipo de ambiente (florestas respirantes, etc.)
 * - Gerencia atores gerados localmente (GeneratedActors array)
 *
 * AURACRONPCGEnvironmentManager - GERENCIADOR GLOBAL:
 * - Controla a rotação automática entre os 3 ambientes principais
 * - Gerencia transições suaves entre ambientes
 * - Coordena múltiplas instâncias de AURACRONPCGEnvironment
 * - Aplica efeitos globais (iluminação, pós-processamento, fog)
 * - Integra com outros sistemas (Phase, Objective, Lane, Jungle)
 * - Controla timing e sequenciamento de mudanças de ambiente
 *
 * RELAÇÃO: Manager (1) -> Environment Instances (N)
 * O Manager orquestra, os Environments executam localmente.
 */
UCLASS()
class AURACRON_API AAURACRONPCGEnvironment : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGEnvironment();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Métodos para integração com sistema de movimento
    /** Verificar se personagem está no ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment|Movement")
    bool IsCharacterInEnvironment(ACharacter* Character) const;

    /** Aplicar efeitos do ambiente ao personagem */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment|Movement")
    void ApplyEnvironmentEffectsToCharacter(ACharacter* Character);

    /** Verificar se há respiradouros geotermais ativos */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Environment|Geothermal")
    bool HasActiveGeothermalVents() const;

    /** Verificar se está próximo a respiradouro geotermal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment|Geothermal")
    bool IsNearGeothermalVent(FVector Location, float Radius) const;

    /** Verificar se está em zona de distorção temporal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment|Temporal")
    bool IsInTemporalDistortionZone(FVector Location) const;

    /** Obter intensidade da distorção temporal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment|Temporal")
    float GetTemporalDistortionIntensity(FVector Location) const;

    // Configurar o tipo de ambiente
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetEnvironmentType(EAURACRONEnvironmentType NewType);

    // Obter o tipo de ambiente
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    EAURACRONEnvironmentType GetEnvironmentType() const { return EnvironmentType; }

    // Gerar o ambiente procedural
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateEnvironment();

    // Atualizar o ambiente com base na fase do mapa
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    // Gerar a Âncora do Purgatório
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PurgatoryRealm")
    void GeneratePurgatoryAnchor();
    
    // Atualizar a Âncora do Purgatório
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PurgatoryRealm")
    void UpdatePurgatoryAnchor(float CurrentTime);

    // Definir a visibilidade do ambiente
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetEnvironmentVisibility(bool bVisible);

    // Definir a escala de atividade do ambiente (0.0 = preview, 1.0 = totalmente ativo)
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetActivityScale(float Scale);

    // Obter escala de atividade atual
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    float GetActivityScale() const { return ActivityScale; }

    // ========================================
    // FUNÇÕES DE INTEGRAÇÃO COM ENVIRONMENTMANAGER
    // ========================================

    /** Registrar este ambiente com o EnvironmentManager */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void RegisterWithEnvironmentManager();

    /** Ativar ambiente (chamado pelo EnvironmentManager) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void ActivateEnvironment();

    /** Desativar ambiente (chamado pelo EnvironmentManager) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void DeactivateEnvironment();

    /** Aplicar transição suave (chamado pelo EnvironmentManager) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void ApplyTransitionEffect(float TransitionProgress, bool bFadingIn);

    /** Obter componente PCG para acesso externo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    UPCGComponent* GetPCGComponent() const { return PCGComponent; }

    /** Verificar se ambiente está ativo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    bool IsEnvironmentActive() const { return ActivityScale > 0.0f; }

    /** Obter localizações táticas para portais */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    TArray<FVector> GetTacticalPortalLocations() const;

    /** Aplicar contração do mapa ao ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Environment")
    void OnMapContraction(float ContractionFactor);

public:
    // Componente PCG principal para geração do ambiente
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    UPCGComponent* PCGComponent;

    // Componente de Post Process para efeitos visuais de boundary
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG|Effects")
    UPostProcessComponent* BoundaryPostProcessComponent;

    // Sistema de partículas Niagara para efeitos de boundary
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Effects")
    UNiagaraSystem* BoundaryParticleSystem;

    // Componente de partículas Niagara para boundary
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG|Effects")
    UNiagaraComponent* BoundaryParticleComponent;

    // Configurações PCG para este ambiente
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG")
    UPCGSettings* EnvironmentSettings;

    // Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|Activity", meta = (ClampMin = "0.0", ClampMax = "3.0"))
    float ActivityScale;

    // Características específicas do ambiente
    // Radiant Plains
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasCrystallinePlateaus;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasLivingCanyons;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasBreathingForests;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|RadiantPlains", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains"))
    bool bHasTectonicBridges;

    // Zephyr Firmament
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasOrbitalArchipelagos;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasAuroraBridges;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasCloudFortresses;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasStellarGardens;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|ZephyrFirmament", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament"))
    bool bHasVoidRifts;

    // Purgatory Realm
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasSpectralPlains;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasRiversOfSouls;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasFragmentedStructures;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|PCG|PurgatoryRealm", meta = (EditCondition = "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm"))
    bool bHasTemporalDistortionZones;

    // Arrays de dados para diferentes tipos de ambiente
    UPROPERTY()
    TArray<FBreathingForestData> BreathingForestData;

    UPROPERTY()
    TArray<FRiverOfSoulsData> RiversOfSoulsData;

    UPROPERTY()
    TArray<FCloudFortressData> CloudFortressData;

    // Variáveis de replicação adicionais
    UPROPERTY(Replicated)
    bool bHasAerialIslands = false;

    UPROPERTY(Replicated)
    bool bHasFloatingCrystals = false;

    UPROPERTY(Replicated)
    bool bHasWindCurrents = false;

    UPROPERTY(Replicated)
    bool bHasAbyssalChasms = false;

    UPROPERTY(Replicated)
    bool bHasEtherealFog = false;

    UPROPERTY(Replicated)
    bool bHasTemporalDistortions = false;

private:
    // Tipo de ambiente
    UPROPERTY(EditAnywhere, Replicated, Category = "AURACRON|PCG")
    EAURACRONEnvironmentType EnvironmentType;

    // Arrays para armazenar atores gerados
    UPROPERTY()
    TArray<AActor*> GeneratedActors;

    // Dados específicos para elementos dinâmicos
    UPROPERTY()
    TArray<FBreathingForestData> BreathingForests;
    









    // Dados para arquipélagos orbitais
    UPROPERTY()
    TArray<FFloatingIslandData> FloatingIslandData;
    
    // Dados para fortalezas de nuvem já declarados acima
    
    // Dados para jardins estelares
    UPROPERTY()
    TArray<FStellarGardenData> StellarGardenData;
    
    // Dados para pontes aurora
    UPROPERTY()
    TArray<FAuroraBridgeData> AuroraBridgeData;
    
    // Dados para rios de almas
    UPROPERTY()
    TArray<FRiverOfSoulsData> RiverOfSoulsData;
    
    // Dados para estruturas fragmentadas
    UPROPERTY()
    TArray<FFragmentedStructureData> FragmentedStructureData;
    
    // Dados para zonas de distorção temporal
    UPROPERTY()
    TArray<FTemporalDistortionData> TemporalDistortionData;
    
    // Dados para nexos sombrios
    UPROPERTY()
    TArray<FShadowNexusData> ShadowNexusData;

    
    // Dados para torres de lamentação
    UPROPERTY()
    TArray<FTowerOfLamentationData> TowerOfLamentationData;
    
    // Dados para o Guardião Espectral
    UPROPERTY()
    FSpectralGuardianData SpectralGuardianData;
    
    // Dados para a Âncora do Purgatório
    UPROPERTY()
    FPurgatoryAnchorData PurgatoryAnchorData;
    
    // Sistema de partículas para as fendas do vazio
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|ZephyrFirmament")
    UNiagaraSystem* VoidRiftParticleSystem;
    
    // Sistema de partículas para as pontes aurora
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|ZephyrFirmament")
    UNiagaraSystem* AuroraBridgeParticleSystem;
    
    // Sistema de partículas para o Guardião Espectral
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* SpectralGuardianParticleSystem;
    
    // Sistema de partículas para a Âncora do Purgatório
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* PurgatoryAnchorParticleSystem;
    
    // Sistema de partículas para os nexos sombrios
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* ShadowNexusParticleSystem;
    
    // Sistema de partículas para as torres de lamentação
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PurgatoryRealm")
    UNiagaraSystem* TowerOfLamentationParticleSystem;

    // ========================================
    // PROPRIEDADES PARA QUALIDADE DE RENDERIZAÇÃO - UE 5.6 MODERN APIS
    // ========================================

    // Indica se é um dispositivo high-end
    UPROPERTY()
    bool bIsHighEndDevice;

    // Contagem máxima de partículas
    UPROPERTY()
    int32 MaxParticleCount;

    // Qualidade da iluminação (1-3)
    UPROPERTY()
    int32 LightingQuality;

    // Qualidade das sombras (1-3)
    UPROPERTY()
    int32 ShadowQuality;

    // Qualidade dos efeitos (1-3)
    UPROPERTY()
    int32 EffectQuality;

    // Intensidade do ambiente
    UPROPERTY()
    float EnvironmentIntensity;

    // Velocidade de transição
    UPROPERTY()
    float TransitionSpeed;

    // Raio de blend
    UPROPERTY()
    float BlendRadius;

    // Taxa de emergência
    UPROPERTY()
    float EmergenceRate;

    // Indica se está emergindo gradualmente
    UPROPERTY()
    bool bIsEmergingGradually;

    // Tempo de início da emergência
    UPROPERTY()
    float EmergenceStartTime;

    // Handle do timer de emergência
    UPROPERTY()
    FTimerHandle EmergenceTimerHandle;

    // Referência para o Phase Manager
    UPROPERTY()
    AAURACRONPCGPhaseManager* PhaseManagerReference;

    // Indica se o blending de ambiente está habilitado
    UPROPERTY()
    bool bEnvironmentBlendingEnabled;

    // Força do blending
    UPROPERTY()
    float BlendingStrength;

    // Indica se transições avançadas estão habilitadas
    UPROPERTY()
    bool bAdvancedTransitionsEnabled;

    // Complexidade da transição
    UPROPERTY()
    float TransitionComplexity;

    // Indica se elementos interativos estão habilitados
    UPROPERTY()
    bool bInteractiveElementsEnabled;

    // Raio de interação
    UPROPERTY()
    float InteractionRadius;

    // Indica se blending de luz está habilitado
    UPROPERTY()
    bool bLightBlendingEnabled;

    // Indica se transições de vento estão habilitadas
    UPROPERTY()
    bool bWindTransitionsEnabled;

    // Indica se interações espectrais estão habilitadas
    UPROPERTY()
    bool bSpectralInteractionsEnabled;

    // Indica se transições suaves estão habilitadas
    UPROPERTY()
    bool bSmoothTransitionsEnabled;

    // Efeito de altitude
    UPROPERTY()
    float AltitudeEffect;

    // Gradiente de transição
    UPROPERTY()
    float TransitionGradient;

    // Indica se transição baseada em altura está habilitada
    UPROPERTY()
    bool bHeightBasedTransitionEnabled;

    // Taxa de transição de partículas
    UPROPERTY()
    float ParticleTransitionRate;

    // Indica se nuvens volumétricas estão habilitadas
    UPROPERTY()
    bool bVolumetricCloudsEnabled;

    // Velocidade de transição das nuvens
    UPROPERTY()
    float CloudTransitionSpeed;

    // Indica se transição para Zephyr está habilitada
    UPROPERTY()
    bool bTransitionToZephyr;

    // Força da transição Zephyr
    UPROPERTY()
    float ZephyrTransitionStrength;

    // Indica se modo simultâneo está habilitado
    UPROPERTY()
    bool bSimultaneousModeEnabled;

    // Prioridade do ambiente
    UPROPERTY()
    int32 EnvironmentPriority;

    // Indica se blending entre ambientes está habilitado
    UPROPERTY()
    bool bCrossEnvironmentBlendingEnabled;

    // Força do blending cruzado
    UPROPERTY()
    float CrossBlendStrength;

    // Funções internas para geração de características específicas
    void GenerateCrystallinePlateaus();

    // Função para atualizar emergência gradual
    void UpdateGradualEmergence();
    void GenerateLivingCanyons();
    void GenerateBreathingForests();
    void GenerateTectonicBridges();
    void GenerateOrbitalArchipelagos();
    void GenerateAuroraBridges();
    void GenerateCloudFortresses();
    void GenerateStellarGardens();
    void GenerateVoidRifts();
    void GenerateSpectralPlains();
    void GenerateRiversOfSouls();
    void GenerateFragmentedStructures();
    void GenerateTemporalDistortionZones();
    void GenerateShadowNexuses();
    void GenerateTowersOfLamentation();
    void GenerateSpectralGuardian();
    void UpdateSpectralGuardian(float CurrentTime);

public:
    // Funções de otimização e configuração
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Optimization")
    void SetDensityScale(float DensityScale);

    // Funções auxiliares para CloudFortresses
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Environment")
    float GetCurrentPhaseIntensity() const;

    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Environment")
    FLinearColor GetPhaseBasedColor() const;

    // Funções para mecânicas invertidas do Reino Purgatório
    void ApplyInvertedPhysicsMechanics(float CurrentTime);
    void ApplyTimeReversalMechanics(float CurrentTime, const FVector& Center, float Radius);
    
    // Função para criar portal de teletransporte para fenda do vazio
    void CreateVoidRiftPortal(const FVector& Position, float Radius, const FLinearColor& Color);

    // ========================================
    // VARIÁVEIS DE ESTADO - UE 5.6 APIS MODERNAS
    // ========================================

public:
    /** Estado de ativação do ambiente */
    UPROPERTY(BlueprintReadOnly, Replicated, Category = "AURACRON|PCG|State")
    bool bIsActive;

    // Se o ambiente possui estruturas sombrias
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|PurgatoryRealm")
    bool bHasShadowStructures;

public:

    // ========================================
    // FUNÇÕES AUXILIARES PARA UE 5.6 - APIS MODERNAS
    // ========================================

    /** Obter mesh específico para âncora baseado no tipo de ambiente */
    UStaticMesh* GetAnchorMeshForEnvironment(EAURACRONEnvironmentType EnvironmentType) const;

    /** Obter material específico para âncora baseado no tipo de ambiente */
    UMaterialInterface* GetAnchorMaterialForEnvironment(EAURACRONEnvironmentType EnvironmentType) const;

    /** Aplicar configurações de qualidade baseadas na performance */
    void ApplyQualitySettings();

    /** Obter configuração do ambiente */
    FAURACRONEnvironmentConfig GetEnvironmentConfiguration() const;

    /** Definir parâmetro PCG usando APIs modernas do UE 5.6 */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Parameters")
    void SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Category = TEXT("Default"));

    /** Obter altura do ambiente em uma posição específica */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Environment")
    float GetEnvironmentHeightAt(const FVector& Location) const;

    // ========================================
    // FUNÇÕES DE BOUNDARY EFFECTS - UE 5.6 MODERN APIS
    // ========================================

    /** Definir intensidade do blur de boundary usando PostProcessComponent */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetBoundaryBlurStrength(float BlurStrength);

    /** Definir raio do blur de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetBoundaryBlurRadius(float BlurRadius);

    /** Habilitar/desabilitar blur de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void EnableBoundaryBlur(bool bEnable);

    /** Definir intensidade do gradiente de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetBoundaryGradientStrength(float GradientStrength);

    /** Definir cor do blur de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetBoundaryBlurColor(const FLinearColor& BlurColor);

    /** Definir tipo de blur de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetBoundaryBlurType(EEnvironmentBlurType BlurType);

    /** Habilitar/desabilitar partículas de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void EnableBoundaryParticles(bool bEnable);

    /** Definir intensidade das partículas de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetBoundaryParticleIntensity(float Intensity);

    /** Habilitar/desabilitar distorção espacial */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void EnableSpaceDistortion(bool bEnable);

    /** Definir intensidade da distorção espacial */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetSpaceDistortionStrength(float Strength);

    /** Atualizar todos os efeitos de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void UpdateBoundaryEffects();



    // Funções auxiliares
    void ClearEnvironment();
    void ApplyActivityScale();
    void UpdateDynamicElements(float DeltaTime);

    // Funções de atualização específicas para elementos dinâmicos
    void UpdateBreathingForests(float Time);
    void UpdateTectonicBridges(float Time);
    void UpdateOrbitalArchipelagos(float Time);
    void UpdateAuroraBridges(float Time);
    void UpdateCloudFortresses(float Time);
    void UpdateStellarGardens(float Time);
    void UpdateRiversOfSouls(float Time);
    void UpdateFragmentedStructures(float Time);
    void UpdateTemporalDistortionZones(float Time);
    void UpdateShadowNexuses(float Time);
    void UpdateTowersOfLamentation(float Time);

    // ========================================
    // MÉTODOS ADICIONAIS PARA APIS MODERNAS UE 5.6
    // ========================================

    // Configuração de dispositivo
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetHighEndDevice(bool bIsHighEnd);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void UpdateRenderingQuality();

    // Configuração de intensidade e transições
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetEnvironmentIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    float GetEnvironmentIntensity() const;

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetTransitionSpeed(float Speed);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetBlendRadius(float Radius);

    // Configuração de emergência gradual
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetEmergenceRate(float Rate);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void StartGradualEmergence();

    // Configuração de propriedades específicas
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetLightIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetParticleSpawnRate(float Rate);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetWindStrength(float Strength);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetCloudDensity(float Density);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetShadowIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetSpectralActivity(float Activity);

    // Configuração de fase
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ApplyPhaseConfiguration(EAURACRONMapPhase Phase);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void UpdateVisualEffects();

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    EAURACRONMapPhase GetCurrentMapPhase() const;

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetCurrentMapPhase(EAURACRONMapPhase Phase);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    bool HasPhaseManagerReference() const;

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetPhaseManagerReference(class AAURACRONPCGPhaseManager* PhaseManager);

    // Configuração de blending
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableEnvironmentBlending(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetBlendingStrength(float Strength);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableAdvancedTransitions(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetTransitionComplexity(float Complexity);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableInteractiveElements(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetInteractionRadius(float Radius);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableLightBlending(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableWindTransitions(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableSpectralInteractions(bool bEnable);

    // Configuração de transições suaves
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableSmoothTransitions(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetAltitudeEffect(float Effect);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetTransitionGradient(float Gradient);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableHeightBasedTransition(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetParticleTransitionRate(float Rate);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableVolumetricClouds(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetCloudTransitionSpeed(float Speed);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void UpdateTransitionSettings();

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetTransitionToZephyr(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetZephyrTransitionStrength(float Strength);

    // Configuração de modo simultâneo
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableSimultaneousMode(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetEnvironmentPriority(int32 Priority);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void EnableCrossEnvironmentBlending(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void SetCrossBlendStrength(float Strength);

private:
    // Propriedades duplicadas removidas - usando as da seção anterior

    UPROPERTY()
    float LightIntensity;

    UPROPERTY()
    float ParticleSpawnRate;

    UPROPERTY()
    float WindStrength;

    UPROPERTY()
    float CloudDensity;

    UPROPERTY()
    float ShadowIntensity;

    UPROPERTY()
    float SpectralActivity;

    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    UPROPERTY()
    class AAURACRONPCGPhaseManager* PhaseManagerRef;

    // Propriedades duplicadas removidas - usando as da seção anterior

    // Propriedades duplicadas removidas - usando as da seção anterior

    // ========================================
    // DECLARAÇÕES DE FUNÇÕES AUXILIARES ADICIONADAS - UE 5.6 MODERN APIS
    // ========================================

    // Timer handle para atualizações otimizadas
    UPROPERTY()
    FTimerHandle UpdateTimerHandle;

    // Função de atualização via Timer (otimização UE 5.6)
    void UpdateDynamicElementsTimer();

    // Funções auxiliares para carregamento assíncrono
    void LoadFortressMeshAsync(UStaticMeshComponent* MeshComponent, int32 FortressIndex);
    void OnFortressMeshLoaded(UStaticMeshComponent* MeshComponent, int32 FortressIndex);

    void LoadStellarGardenMeshAsync(UStaticMeshComponent* MeshComponent, int32 GardenIndex);
    void OnStellarGardenMeshLoaded(UStaticMeshComponent* MeshComponent, int32 GardenIndex);

    void LoadSpectralZoneMeshAsync(UStaticMeshComponent* MeshComponent, int32 ZoneIndex);
    void OnSpectralZoneMeshLoaded(UStaticMeshComponent* MeshComponent, int32 ZoneIndex);

    // Funções auxiliares para criação de sistemas de partículas
    void CreateStellarGardenParticleSystem(AStaticMeshActor* GardenActor, int32 GardenIndex);
    void CreateSpectralZoneParticleSystem(AStaticMeshActor* ZoneActor, int32 ZoneIndex, float ZoneRadius);
    void CreateSpectralDistortionEffect(AStaticMeshActor* DistortionActor, int32 DistortionIndex);

    // Funções auxiliares para Reino Purgatório
    void CreateSpectralZone(const FVector& ZoneCenter, float ZoneRadius, int32 ZoneIndex);
    void CreateSpectralFogEffects(const FVector& EnvironmentCenter, float EnvironmentRadius);
    void CreateSpectralDistortions(const FVector& EnvironmentCenter, float EnvironmentRadius);

    // Funções auxiliares para Rios de Almas
    void LoadRiverOfSoulsMeshAsync(UStaticMeshComponent* MeshComponent, int32 RiverIndex);
    void OnRiverOfSoulsMeshLoaded(UStaticMeshComponent* MeshComponent, int32 RiverIndex);
    void CreateRiverOfSoulsParticleSystem(AStaticMeshActor* RiverActor, const TArray<FVector>& FlowPath, int32 RiverIndex);

    // Funções auxiliares para Estruturas Fragmentadas
    void LoadFragmentedStructureMeshAsync(UStaticMeshComponent* MeshComponent, int32 StructureIndex);
    void OnFragmentedStructureMeshLoaded(UStaticMeshComponent* MeshComponent, int32 StructureIndex);
    void CreateFragmentedStructureEffects(AStaticMeshActor* StructureActor, int32 StructureIndex);
};