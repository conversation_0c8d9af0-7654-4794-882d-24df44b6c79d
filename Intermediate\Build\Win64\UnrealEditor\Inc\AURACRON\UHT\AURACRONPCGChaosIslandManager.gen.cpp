// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGChaosIslandManager.h"
#include "UObject/CoreNet.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGChaosIslandManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnChaosManagerFullyInitialized ***************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates para eventos do sistema\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para eventos do sistema" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnChaosManagerFullyInitialized__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnChaosManagerFullyInitialized_DelegateWrapper(const FMulticastScriptDelegate& OnChaosManagerFullyInitialized)
{
	OnChaosManagerFullyInitialized.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnChaosManagerFullyInitialized *****************************************

// ********** Begin Delegate FOnChaosIslandsListChanged ********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnChaosIslandsListChanged_Parms
	{
		TArray<AAURACRONPCGChaosIsland*> Islands;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Islands_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Islands_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Islands;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::NewProp_Islands_Inner = { "Islands", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::NewProp_Islands = { "Islands", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnChaosIslandsListChanged_Parms, Islands), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Islands_MetaData), NewProp_Islands_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::NewProp_Islands_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::NewProp_Islands,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnChaosIslandsListChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnChaosIslandsListChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnChaosIslandsListChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnChaosIslandsListChanged_DelegateWrapper(const FMulticastScriptDelegate& OnChaosIslandsListChanged, TArray<AAURACRONPCGChaosIsland*> const& Islands)
{
	struct _Script_AURACRON_eventOnChaosIslandsListChanged_Parms
	{
		TArray<AAURACRONPCGChaosIsland*> Islands;
	};
	_Script_AURACRON_eventOnChaosIslandsListChanged_Parms Parms;
	Parms.Islands=Islands;
	OnChaosIslandsListChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChaosIslandsListChanged **********************************************

// ********** Begin Delegate FOnMapPhaseUpdated ****************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnMapPhaseUpdated_Parms
	{
		EAURACRONMapPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnMapPhaseUpdated_Parms, NewPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnMapPhaseUpdated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnMapPhaseUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::_Script_AURACRON_eventOnMapPhaseUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapPhaseUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnMapPhaseUpdated, EAURACRONMapPhase NewPhase)
{
	struct _Script_AURACRON_eventOnMapPhaseUpdated_Parms
	{
		EAURACRONMapPhase NewPhase;
	};
	_Script_AURACRON_eventOnMapPhaseUpdated_Parms Parms;
	Parms.NewPhase=NewPhase;
	OnMapPhaseUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapPhaseUpdated ******************************************************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function CalculateLineIntersection ********
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics
{
	struct AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms
	{
		FVector P1;
		FVector P2;
		FVector P3;
		FVector P4;
		FVector OutIntersection;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular interse\xc3\xa7\xc3\xa3o entre duas linhas 2D */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular interse\xc3\xa7\xc3\xa3o entre duas linhas 2D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_P1_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_P2_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_P3_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_P4_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_P1;
	static const UECodeGen_Private::FStructPropertyParams NewProp_P2;
	static const UECodeGen_Private::FStructPropertyParams NewProp_P3;
	static const UECodeGen_Private::FStructPropertyParams NewProp_P4;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutIntersection;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P1 = { "P1", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms, P1), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_P1_MetaData), NewProp_P1_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P2 = { "P2", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms, P2), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_P2_MetaData), NewProp_P2_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P3 = { "P3", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms, P3), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_P3_MetaData), NewProp_P3_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P4 = { "P4", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms, P4), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_P4_MetaData), NewProp_P4_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_OutIntersection = { "OutIntersection", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms, OutIntersection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms), &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P3,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_P4,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_OutIntersection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "CalculateLineIntersection", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x40C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::AURACRONPCGChaosIslandManager_eventCalculateLineIntersection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execCalculateLineIntersection)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_P1);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_P2);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_P3);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_P4);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_OutIntersection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CalculateLineIntersection(Z_Param_Out_P1,Z_Param_Out_P2,Z_Param_Out_P3,Z_Param_Out_P4,Z_Param_Out_OutIntersection);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function CalculateLineIntersection **********

// ********** Begin Class AAURACRONPCGChaosIslandManager Function ClientNotifyAllIslandsDestroyed **
static FName NAME_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed = FName(TEXT("ClientNotifyAllIslandsDestroyed"));
void AAURACRONPCGChaosIslandManager::ClientNotifyAllIslandsDestroyed()
{
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para notificar clientes sobre destrui\xc3\xa7\xc3\xa3o de ilhas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para notificar clientes sobre destrui\xc3\xa7\xc3\xa3o de ilhas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "ClientNotifyAllIslandsDestroyed", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x01020CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execClientNotifyAllIslandsDestroyed)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClientNotifyAllIslandsDestroyed_Implementation();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function ClientNotifyAllIslandsDestroyed ****

// ********** Begin Class AAURACRONPCGChaosIslandManager Function ClientNotifyIslandSpawned ********
struct AURACRONPCGChaosIslandManager_eventClientNotifyIslandSpawned_Parms
{
	AAURACRONPCGChaosIsland* Island;
	FVector Location;
};
static FName NAME_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned = FName(TEXT("ClientNotifyIslandSpawned"));
void AAURACRONPCGChaosIslandManager::ClientNotifyIslandSpawned(AAURACRONPCGChaosIsland* Island, FVector const& Location)
{
	AURACRONPCGChaosIslandManager_eventClientNotifyIslandSpawned_Parms Parms;
	Parms.Island=Island;
	Parms.Location=Location;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para notificar clientes sobre nova ilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para notificar clientes sobre nova ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Island;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::NewProp_Island = { "Island", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventClientNotifyIslandSpawned_Parms, Island), Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventClientNotifyIslandSpawned_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::NewProp_Island,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "ClientNotifyIslandSpawned", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::PropPointers), sizeof(AURACRONPCGChaosIslandManager_eventClientNotifyIslandSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x01820CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGChaosIslandManager_eventClientNotifyIslandSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execClientNotifyIslandSpawned)
{
	P_GET_OBJECT(AAURACRONPCGChaosIsland,Z_Param_Island);
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClientNotifyIslandSpawned_Implementation(Z_Param_Island,Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function ClientNotifyIslandSpawned **********

// ********** Begin Class AAURACRONPCGChaosIslandManager Function FindAllFlowIntersections *********
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics
{
	struct AURACRONPCGChaosIslandManager_eventFindAllFlowIntersections_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Encontrar todos os pontos de interse\xc3\xa7\xc3\xa3o do fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Encontrar todos os pontos de interse\xc3\xa7\xc3\xa3o do fluxo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventFindAllFlowIntersections_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "FindAllFlowIntersections", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::AURACRONPCGChaosIslandManager_eventFindAllFlowIntersections_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::AURACRONPCGChaosIslandManager_eventFindAllFlowIntersections_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execFindAllFlowIntersections)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindAllFlowIntersections();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function FindAllFlowIntersections ***********

// ********** Begin Class AAURACRONPCGChaosIslandManager Function GenerateChaosIslands *************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GenerateChaosIslands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar as ilhas caos nos pontos de interse\xc3\xa7\xc3\xa3o do fluxo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar as ilhas caos nos pontos de interse\xc3\xa7\xc3\xa3o do fluxo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GenerateChaosIslands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "GenerateChaosIslands", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GenerateChaosIslands_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GenerateChaosIslands_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GenerateChaosIslands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GenerateChaosIslands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execGenerateChaosIslands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateChaosIslands();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function GenerateChaosIslands ***************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function GetAllChaosIslands ***************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics
{
	struct AURACRONPCGChaosIslandManager_eventGetAllChaosIslands_Parms
	{
		TArray<AAURACRONPCGChaosIsland*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todas as ilhas caos - CORRIGIDO: usar AAURACRONPCGChaosIsland */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todas as ilhas caos - CORRIGIDO: usar AAURACRONPCGChaosIsland" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventGetAllChaosIslands_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "GetAllChaosIslands", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::AURACRONPCGChaosIslandManager_eventGetAllChaosIslands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::AURACRONPCGChaosIslandManager_eventGetAllChaosIslands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execGetAllChaosIslands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AAURACRONPCGChaosIsland*>*)Z_Param__Result=P_THIS->GetAllChaosIslands();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function GetAllChaosIslands *****************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function GetAllChaosPortals ***************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics
{
	struct AURACRONPCGChaosIslandManager_eventGetAllChaosPortals_Parms
	{
		TArray<AAURACRONPCGChaosPortal*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todos os portais caos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todos os portais caos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventGetAllChaosPortals_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "GetAllChaosPortals", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::AURACRONPCGChaosIslandManager_eventGetAllChaosPortals_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::AURACRONPCGChaosIslandManager_eventGetAllChaosPortals_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execGetAllChaosPortals)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AAURACRONPCGChaosPortal*>*)Z_Param__Result=P_THIS->GetAllChaosPortals();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function GetAllChaosPortals *****************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function Initialize ***********************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics
{
	struct AURACRONPCGChaosIslandManager_eventInitialize_Parms
	{
		AAURACRONPCGPrismalFlow* InPrismalFlow;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar o gerenciador com refer\xc3\xaancia ao fluxo prismal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar o gerenciador com refer\xc3\xaancia ao fluxo prismal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InPrismalFlow;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::NewProp_InPrismalFlow = { "InPrismalFlow", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventInitialize_Parms, InPrismalFlow), Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::NewProp_InPrismalFlow,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "Initialize", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::AURACRONPCGChaosIslandManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::AURACRONPCGChaosIslandManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execInitialize)
{
	P_GET_OBJECT(AAURACRONPCGPrismalFlow,Z_Param_InPrismalFlow);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_InPrismalFlow);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function Initialize *************************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function IsPointAtFlowIntersection ********
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics
{
	struct AURACRONPCGChaosIslandManager_eventIsPointAtFlowIntersection_Parms
	{
		FVector Point;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se um ponto est\xc3\xa1 em uma interse\xc3\xa7\xc3\xa3o do fluxo */" },
#endif
		{ "CPP_Default_Tolerance", "500.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se um ponto est\xc3\xa1 em uma interse\xc3\xa7\xc3\xa3o do fluxo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventIsPointAtFlowIntersection_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventIsPointAtFlowIntersection_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGChaosIslandManager_eventIsPointAtFlowIntersection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGChaosIslandManager_eventIsPointAtFlowIntersection_Parms), &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "IsPointAtFlowIntersection", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::AURACRONPCGChaosIslandManager_eventIsPointAtFlowIntersection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::AURACRONPCGChaosIslandManager_eventIsPointAtFlowIntersection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execIsPointAtFlowIntersection)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPointAtFlowIntersection(Z_Param_Out_Point,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function IsPointAtFlowIntersection **********

// ********** Begin Class AAURACRONPCGChaosIslandManager Function IsPointTooCloseToExistingIslands *
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics
{
	struct AURACRONPCGChaosIslandManager_eventIsPointTooCloseToExistingIslands_Parms
	{
		FVector Point;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se um ponto est\xc3\xa1 muito pr\xc3\xb3ximo de ilhas existentes */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se um ponto est\xc3\xa1 muito pr\xc3\xb3ximo de ilhas existentes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventIsPointTooCloseToExistingIslands_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
void Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGChaosIslandManager_eventIsPointTooCloseToExistingIslands_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGChaosIslandManager_eventIsPointTooCloseToExistingIslands_Parms), &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "IsPointTooCloseToExistingIslands", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::AURACRONPCGChaosIslandManager_eventIsPointTooCloseToExistingIslands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x40C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::AURACRONPCGChaosIslandManager_eventIsPointTooCloseToExistingIslands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execIsPointTooCloseToExistingIslands)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPointTooCloseToExistingIslands(Z_Param_Out_Point);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function IsPointTooCloseToExistingIslands ***

// ********** Begin Class AAURACRONPCGChaosIslandManager Function OnRep_ChaosIslandsUpdated ********
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_ChaosIslandsUpdated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback de replica\xc3\xa7\xc3\xa3o para lista de ilhas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback de replica\xc3\xa7\xc3\xa3o para lista de ilhas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_ChaosIslandsUpdated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "OnRep_ChaosIslandsUpdated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_ChaosIslandsUpdated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_ChaosIslandsUpdated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_ChaosIslandsUpdated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_ChaosIslandsUpdated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execOnRep_ChaosIslandsUpdated)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ChaosIslandsUpdated();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function OnRep_ChaosIslandsUpdated **********

// ********** Begin Class AAURACRONPCGChaosIslandManager Function OnRep_MapPhaseChanged ************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_MapPhaseChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback de replica\xc3\xa7\xc3\xa3o para mudan\xc3\xa7""a de fase */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback de replica\xc3\xa7\xc3\xa3o para mudan\xc3\xa7""a de fase" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_MapPhaseChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "OnRep_MapPhaseChanged", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_MapPhaseChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_MapPhaseChanged_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_MapPhaseChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_MapPhaseChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execOnRep_MapPhaseChanged)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MapPhaseChanged();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function OnRep_MapPhaseChanged **************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function ServerDestroyAllChaosIslands *****
static FName NAME_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands = FName(TEXT("ServerDestroyAllChaosIslands"));
void AAURACRONPCGChaosIslandManager::ServerDestroyAllChaosIslands()
{
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para destruir todas as ilhas no servidor */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para destruir todas as ilhas no servidor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "ServerDestroyAllChaosIslands", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x84220CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execServerDestroyAllChaosIslands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerDestroyAllChaosIslands_Validate())
	{
		RPC_ValidateFailed(TEXT("ServerDestroyAllChaosIslands_Validate"));
		return;
	}
	P_THIS->ServerDestroyAllChaosIslands_Implementation();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function ServerDestroyAllChaosIslands *******

// ********** Begin Class AAURACRONPCGChaosIslandManager Function ServerSpawnChaosIsland ***********
struct AURACRONPCGChaosIslandManager_eventServerSpawnChaosIsland_Parms
{
	FVector Location;
};
static FName NAME_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland = FName(TEXT("ServerSpawnChaosIsland"));
void AAURACRONPCGChaosIslandManager::ServerSpawnChaosIsland(FVector const& Location)
{
	AURACRONPCGChaosIslandManager_eventServerSpawnChaosIsland_Parms Parms;
	Parms.Location=Location;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC para spawnar ilha no servidor */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC para spawnar ilha no servidor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventServerSpawnChaosIsland_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "ServerSpawnChaosIsland", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::PropPointers), sizeof(AURACRONPCGChaosIslandManager_eventServerSpawnChaosIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x84A20CC0, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGChaosIslandManager_eventServerSpawnChaosIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execServerSpawnChaosIsland)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	if (!P_THIS->ServerSpawnChaosIsland_Validate(Z_Param_Location))
	{
		RPC_ValidateFailed(TEXT("ServerSpawnChaosIsland_Validate"));
		return;
	}
	P_THIS->ServerSpawnChaosIsland_Implementation(Z_Param_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function ServerSpawnChaosIsland *************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function SetAllChaosIslandsActive *********
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics
{
	struct AURACRONPCGChaosIslandManager_eventSetAllChaosIslandsActive_Parms
	{
		bool bActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar/desativar todas as ilhas caos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar/desativar todas as ilhas caos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((AURACRONPCGChaosIslandManager_eventSetAllChaosIslandsActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGChaosIslandManager_eventSetAllChaosIslandsActive_Parms), &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::NewProp_bActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "SetAllChaosIslandsActive", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::AURACRONPCGChaosIslandManager_eventSetAllChaosIslandsActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::AURACRONPCGChaosIslandManager_eventSetAllChaosIslandsActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execSetAllChaosIslandsActive)
{
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAllChaosIslandsActive(Z_Param_bActive);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function SetAllChaosIslandsActive ***********

// ********** Begin Class AAURACRONPCGChaosIslandManager Function SetAllChaosPortalsActive *********
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics
{
	struct AURACRONPCGChaosIslandManager_eventSetAllChaosPortalsActive_Parms
	{
		bool bActive;
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar/desativar todos os portais caos */" },
#endif
		{ "CPP_Default_Duration", "0.000000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar/desativar todos os portais caos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((AURACRONPCGChaosIslandManager_eventSetAllChaosPortalsActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGChaosIslandManager_eventSetAllChaosPortalsActive_Parms), &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventSetAllChaosPortalsActive_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventSetAllChaosPortalsActive_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_bActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "SetAllChaosPortalsActive", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::AURACRONPCGChaosIslandManager_eventSetAllChaosPortalsActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::AURACRONPCGChaosIslandManager_eventSetAllChaosPortalsActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execSetAllChaosPortalsActive)
{
	P_GET_UBOOL(Z_Param_bActive);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAllChaosPortalsActive(Z_Param_bActive,Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function SetAllChaosPortalsActive ***********

// ********** Begin Class AAURACRONPCGChaosIslandManager Function SpawnChaosIsland *****************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics
{
	struct AURACRONPCGChaosIslandManager_eventSpawnChaosIsland_Parms
	{
		FVector Location;
		AAURACRONPCGChaosIsland* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar uma ilha caos em um ponto espec\xc3\xad""fico - CORRIGIDO: usar AAURACRONPCGChaosIsland */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar uma ilha caos em um ponto espec\xc3\xad""fico - CORRIGIDO: usar AAURACRONPCGChaosIsland" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventSpawnChaosIsland_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventSpawnChaosIsland_Parms, ReturnValue), Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "SpawnChaosIsland", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::AURACRONPCGChaosIslandManager_eventSpawnChaosIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::AURACRONPCGChaosIslandManager_eventSpawnChaosIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execSpawnChaosIsland)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AAURACRONPCGChaosIsland**)Z_Param__Result=P_THIS->SpawnChaosIsland(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function SpawnChaosIsland *******************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function SpawnChaosPortal *****************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics
{
	struct AURACRONPCGChaosIslandManager_eventSpawnChaosPortal_Parms
	{
		FVector Location;
		AAURACRONPCGChaosPortal* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar um portal caos em um ponto espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar um portal caos em um ponto espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventSpawnChaosPortal_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventSpawnChaosPortal_Parms, ReturnValue), Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "SpawnChaosPortal", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::AURACRONPCGChaosIslandManager_eventSpawnChaosPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::AURACRONPCGChaosIslandManager_eventSpawnChaosPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execSpawnChaosPortal)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AAURACRONPCGChaosPortal**)Z_Param__Result=P_THIS->SpawnChaosPortal(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function SpawnChaosPortal *******************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function UpdateEffectsIntensity ***********
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateEffectsIntensity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar a intensidade dos efeitos baseado na fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar a intensidade dos efeitos baseado na fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateEffectsIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "UpdateEffectsIntensity", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateEffectsIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateEffectsIntensity_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateEffectsIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateEffectsIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execUpdateEffectsIntensity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateEffectsIntensity();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function UpdateEffectsIntensity *************

// ********** Begin Class AAURACRONPCGChaosIslandManager Function UpdateForMapPhase ****************
struct Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics
{
	struct AURACRONPCGChaosIslandManager_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar as ilhas caos para a fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar as ilhas caos para a fase atual do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGChaosIslandManager_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGChaosIslandManager, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::AURACRONPCGChaosIslandManager_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::AURACRONPCGChaosIslandManager_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGChaosIslandManager::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGChaosIslandManager Function UpdateForMapPhase ******************

// ********** Begin Class AAURACRONPCGChaosIslandManager *******************************************
void AAURACRONPCGChaosIslandManager::StaticRegisterNativesAAURACRONPCGChaosIslandManager()
{
	UClass* Class = AAURACRONPCGChaosIslandManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateLineIntersection", &AAURACRONPCGChaosIslandManager::execCalculateLineIntersection },
		{ "ClientNotifyAllIslandsDestroyed", &AAURACRONPCGChaosIslandManager::execClientNotifyAllIslandsDestroyed },
		{ "ClientNotifyIslandSpawned", &AAURACRONPCGChaosIslandManager::execClientNotifyIslandSpawned },
		{ "FindAllFlowIntersections", &AAURACRONPCGChaosIslandManager::execFindAllFlowIntersections },
		{ "GenerateChaosIslands", &AAURACRONPCGChaosIslandManager::execGenerateChaosIslands },
		{ "GetAllChaosIslands", &AAURACRONPCGChaosIslandManager::execGetAllChaosIslands },
		{ "GetAllChaosPortals", &AAURACRONPCGChaosIslandManager::execGetAllChaosPortals },
		{ "Initialize", &AAURACRONPCGChaosIslandManager::execInitialize },
		{ "IsPointAtFlowIntersection", &AAURACRONPCGChaosIslandManager::execIsPointAtFlowIntersection },
		{ "IsPointTooCloseToExistingIslands", &AAURACRONPCGChaosIslandManager::execIsPointTooCloseToExistingIslands },
		{ "OnRep_ChaosIslandsUpdated", &AAURACRONPCGChaosIslandManager::execOnRep_ChaosIslandsUpdated },
		{ "OnRep_MapPhaseChanged", &AAURACRONPCGChaosIslandManager::execOnRep_MapPhaseChanged },
		{ "ServerDestroyAllChaosIslands", &AAURACRONPCGChaosIslandManager::execServerDestroyAllChaosIslands },
		{ "ServerSpawnChaosIsland", &AAURACRONPCGChaosIslandManager::execServerSpawnChaosIsland },
		{ "SetAllChaosIslandsActive", &AAURACRONPCGChaosIslandManager::execSetAllChaosIslandsActive },
		{ "SetAllChaosPortalsActive", &AAURACRONPCGChaosIslandManager::execSetAllChaosPortalsActive },
		{ "SpawnChaosIsland", &AAURACRONPCGChaosIslandManager::execSpawnChaosIsland },
		{ "SpawnChaosPortal", &AAURACRONPCGChaosIslandManager::execSpawnChaosPortal },
		{ "UpdateEffectsIntensity", &AAURACRONPCGChaosIslandManager::execUpdateEffectsIntensity },
		{ "UpdateForMapPhase", &AAURACRONPCGChaosIslandManager::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager;
UClass* AAURACRONPCGChaosIslandManager::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGChaosIslandManager;
	if (!Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGChaosIslandManager"),
			Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGChaosIslandManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager_NoRegister()
{
	return AAURACRONPCGChaosIslandManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador de Ilhas Caos\n * Respons\xc3\xa1vel por coordenar a gera\xc3\xa7\xc3\xa3o, posicionamento e comportamento das Ilhas Caos\n * Garante que as ilhas estejam corretamente posicionadas em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n * Implementa os requisitos do GDD:\n * - Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n * - Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n * - Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGChaosIslandManager.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador de Ilhas Caos\nRespons\xc3\xa1vel por coordenar a gera\xc3\xa7\xc3\xa3o, posicionamento e comportamento das Ilhas Caos\nGarante que as ilhas estejam corretamente posicionadas em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\nImplementa os requisitos do GDD:\n- Localiza\xc3\xa7\xc3\xa3o: Em pontos de interse\xc3\xa7\xc3\xa3o do Fluxo\n- Caracter\xc3\xadsticas: Perigos ambientais, recompensas de alto risco, terreno inst\xc3\xa1vel\n- Valor Estrat\xc3\xa9gico: Itens que mudam o jogo com risco significativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChaosManagerFullyInitialized_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando o manager est\xc3\xa1 totalmente inicializado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando o manager est\xc3\xa1 totalmente inicializado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChaosIslandsListChanged_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando a lista de ilhas muda */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando a lista de ilhas muda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapPhaseUpdated_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando a fase do mapa muda */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando a fase do mapa muda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlow_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia ao fluxo prismal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao fluxo prismal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosIslands_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lista de ilhas caos - CORRIGIDO: usar AAURACRONPCGChaosIsland */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lista de ilhas caos - CORRIGIDO: usar AAURACRONPCGChaosIsland" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosPortals_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lista de portais caos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lista de portais caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxChaosIslands_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de ilhas caos (conforme GDD) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de ilhas caos (conforme GDD)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosIslandClass_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Classe da ilha caos para spawn - CORRIGIDO: usar AAURACRONPCGChaosIsland */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe da ilha caos para spawn - CORRIGIDO: usar AAURACRONPCGChaosIsland" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosPortalClass_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Classe do portal caos para spawn */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe do portal caos para spawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDistanceBetweenIslands_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xadnima entre ilhas caos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xadnima entre ilhas caos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoSpawnPortals_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Auto-spawnar portais quando criar ilhas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-spawnar portais quando criar ilhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioComponent_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de \xc3\xa1udio para efeitos ambiente */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de \xc3\xa1udio para efeitos ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalVFXComponent_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de VFX global usando Niagara */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de VFX global usando Niagara" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightComponent_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de ilumina\xc3\xa7\xc3\xa3o ambiente */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de ilumina\xc3\xa7\xc3\xa3o ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedIntersectionPoints_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cache de pontos de interse\xc3\xa7\xc3\xa3o para performance */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache de pontos de interse\xc3\xa7\xc3\xa3o para performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastIntersectionCalculationTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp da \xc3\xbaltima calcula\xc3\xa7\xc3\xa3o de interse\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp da \xc3\xbaltima calcula\xc3\xa7\xc3\xa3o de interse\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntersectionCacheValidDuration_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o de validade do cache de interse\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o de validade do cache de interse\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSystemFullyInitialized_MetaData[] = {
		{ "Category", "AURACRON|ChaosIsland|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Indica se o sistema est\xc3\xa1 totalmente inicializado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGChaosIslandManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Indica se o sistema est\xc3\xa1 totalmente inicializado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChaosManagerFullyInitialized;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChaosIslandsListChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapPhaseUpdated;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PrismalFlow;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosIslands_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChaosIslands;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosPortals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChaosPortals;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxChaosIslands;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ChaosIslandClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ChaosPortalClass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistanceBetweenIslands;
	static void NewProp_bAutoSpawnPortals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoSpawnPortals;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AudioComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GlobalVFXComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AmbientLightComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CachedIntersectionPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CachedIntersectionPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastIntersectionCalculationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IntersectionCacheValidDuration;
	static void NewProp_bSystemFullyInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSystemFullyInitialized;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_CalculateLineIntersection, "CalculateLineIntersection" }, // 1109122917
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyAllIslandsDestroyed, "ClientNotifyAllIslandsDestroyed" }, // 2148827581
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ClientNotifyIslandSpawned, "ClientNotifyIslandSpawned" }, // 3349981771
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_FindAllFlowIntersections, "FindAllFlowIntersections" }, // 3413342752
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GenerateChaosIslands, "GenerateChaosIslands" }, // 481140419
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosIslands, "GetAllChaosIslands" }, // 4066087341
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_GetAllChaosPortals, "GetAllChaosPortals" }, // 3317228242
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_Initialize, "Initialize" }, // 3416575117
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointAtFlowIntersection, "IsPointAtFlowIntersection" }, // 3269165531
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_IsPointTooCloseToExistingIslands, "IsPointTooCloseToExistingIslands" }, // 3524397695
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_ChaosIslandsUpdated, "OnRep_ChaosIslandsUpdated" }, // 2338988451
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_OnRep_MapPhaseChanged, "OnRep_MapPhaseChanged" }, // 477315359
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerDestroyAllChaosIslands, "ServerDestroyAllChaosIslands" }, // 1289725494
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_ServerSpawnChaosIsland, "ServerSpawnChaosIsland" }, // 3259433512
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosIslandsActive, "SetAllChaosIslandsActive" }, // 168602348
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SetAllChaosPortalsActive, "SetAllChaosPortalsActive" }, // 3008753483
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosIsland, "SpawnChaosIsland" }, // 2889637954
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_SpawnChaosPortal, "SpawnChaosPortal" }, // 125133564
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateEffectsIntensity, "UpdateEffectsIntensity" }, // 3432417707
		{ &Z_Construct_UFunction_AAURACRONPCGChaosIslandManager_UpdateForMapPhase, "UpdateForMapPhase" }, // 585437670
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGChaosIslandManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_OnChaosManagerFullyInitialized = { "OnChaosManagerFullyInitialized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, OnChaosManagerFullyInitialized), Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChaosManagerFullyInitialized_MetaData), NewProp_OnChaosManagerFullyInitialized_MetaData) }; // 2611356234
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_OnChaosIslandsListChanged = { "OnChaosIslandsListChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, OnChaosIslandsListChanged), Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChaosIslandsListChanged_MetaData), NewProp_OnChaosIslandsListChanged_MetaData) }; // 3886946584
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_OnMapPhaseUpdated = { "OnMapPhaseUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, OnMapPhaseUpdated), Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapPhaseUpdated_MetaData), NewProp_OnMapPhaseUpdated_MetaData) }; // 1065569218
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_PrismalFlow = { "PrismalFlow", nullptr, (EPropertyFlags)0x0020080000000020, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, PrismalFlow), Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlow_MetaData), NewProp_PrismalFlow_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosIslands_Inner = { "ChaosIslands", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosIslands = { "ChaosIslands", nullptr, (EPropertyFlags)0x0020080000000020, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, ChaosIslands), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosIslands_MetaData), NewProp_ChaosIslands_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosPortals_Inner = { "ChaosPortals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosPortals = { "ChaosPortals", nullptr, (EPropertyFlags)0x0020080000000020, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, ChaosPortals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosPortals_MetaData), NewProp_ChaosPortals_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_MaxChaosIslands = { "MaxChaosIslands", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, MaxChaosIslands), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxChaosIslands_MetaData), NewProp_MaxChaosIslands_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosIslandClass = { "ChaosIslandClass", nullptr, (EPropertyFlags)0x0024080000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, ChaosIslandClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AAURACRONPCGChaosIsland_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosIslandClass_MetaData), NewProp_ChaosIslandClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosPortalClass = { "ChaosPortalClass", nullptr, (EPropertyFlags)0x0024080000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, ChaosPortalClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AAURACRONPCGChaosPortal_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosPortalClass_MetaData), NewProp_ChaosPortalClass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_MinDistanceBetweenIslands = { "MinDistanceBetweenIslands", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, MinDistanceBetweenIslands), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDistanceBetweenIslands_MetaData), NewProp_MinDistanceBetweenIslands_MetaData) };
void Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bAutoSpawnPortals_SetBit(void* Obj)
{
	((AAURACRONPCGChaosIslandManager*)Obj)->bAutoSpawnPortals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bAutoSpawnPortals = { "bAutoSpawnPortals", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGChaosIslandManager), &Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bAutoSpawnPortals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoSpawnPortals_MetaData), NewProp_bAutoSpawnPortals_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", "OnRep_MapPhaseChanged", (EPropertyFlags)0x0020080100000020, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_AudioComponent = { "AudioComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, AudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioComponent_MetaData), NewProp_AudioComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_GlobalVFXComponent = { "GlobalVFXComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, GlobalVFXComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalVFXComponent_MetaData), NewProp_GlobalVFXComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_AmbientLightComponent = { "AmbientLightComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, AmbientLightComponent), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightComponent_MetaData), NewProp_AmbientLightComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CachedIntersectionPoints_Inner = { "CachedIntersectionPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CachedIntersectionPoints = { "CachedIntersectionPoints", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, CachedIntersectionPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedIntersectionPoints_MetaData), NewProp_CachedIntersectionPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_LastIntersectionCalculationTime = { "LastIntersectionCalculationTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, LastIntersectionCalculationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastIntersectionCalculationTime_MetaData), NewProp_LastIntersectionCalculationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_IntersectionCacheValidDuration = { "IntersectionCacheValidDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGChaosIslandManager, IntersectionCacheValidDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntersectionCacheValidDuration_MetaData), NewProp_IntersectionCacheValidDuration_MetaData) };
void Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bSystemFullyInitialized_SetBit(void* Obj)
{
	((AAURACRONPCGChaosIslandManager*)Obj)->bSystemFullyInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bSystemFullyInitialized = { "bSystemFullyInitialized", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGChaosIslandManager), &Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bSystemFullyInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSystemFullyInitialized_MetaData), NewProp_bSystemFullyInitialized_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_OnChaosManagerFullyInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_OnChaosIslandsListChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_OnMapPhaseUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_PrismalFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosIslands_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosIslands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosPortals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosPortals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_MaxChaosIslands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosIslandClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_ChaosPortalClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_MinDistanceBetweenIslands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bAutoSpawnPortals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_AudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_GlobalVFXComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_AmbientLightComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CachedIntersectionPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_CachedIntersectionPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_LastIntersectionCalculationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_IntersectionCacheValidDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::NewProp_bSystemFullyInitialized,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::ClassParams = {
	&AAURACRONPCGChaosIslandManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGChaosIslandManager()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager.OuterSingleton, Z_Construct_UClass_AAURACRONPCGChaosIslandManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGChaosIslandManager::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_PrismalFlow(TEXT("PrismalFlow"));
	static FName Name_ChaosIslands(TEXT("ChaosIslands"));
	static FName Name_ChaosPortals(TEXT("ChaosPortals"));
	static FName Name_MaxChaosIslands(TEXT("MaxChaosIslands"));
	static FName Name_bAutoSpawnPortals(TEXT("bAutoSpawnPortals"));
	static FName Name_CurrentMapPhase(TEXT("CurrentMapPhase"));
	const bool bIsValid = true
		&& Name_PrismalFlow == ClassReps[(int32)ENetFields_Private::PrismalFlow].Property->GetFName()
		&& Name_ChaosIslands == ClassReps[(int32)ENetFields_Private::ChaosIslands].Property->GetFName()
		&& Name_ChaosPortals == ClassReps[(int32)ENetFields_Private::ChaosPortals].Property->GetFName()
		&& Name_MaxChaosIslands == ClassReps[(int32)ENetFields_Private::MaxChaosIslands].Property->GetFName()
		&& Name_bAutoSpawnPortals == ClassReps[(int32)ENetFields_Private::bAutoSpawnPortals].Property->GetFName()
		&& Name_CurrentMapPhase == ClassReps[(int32)ENetFields_Private::CurrentMapPhase].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGChaosIslandManager"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGChaosIslandManager);
AAURACRONPCGChaosIslandManager::~AAURACRONPCGChaosIslandManager() {}
// ********** End Class AAURACRONPCGChaosIslandManager *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGChaosIslandManager, AAURACRONPCGChaosIslandManager::StaticClass, TEXT("AAURACRONPCGChaosIslandManager"), &Z_Registration_Info_UClass_AAURACRONPCGChaosIslandManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGChaosIslandManager), 2585234624U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h__Script_AURACRON_2605466568(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGChaosIslandManager_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
