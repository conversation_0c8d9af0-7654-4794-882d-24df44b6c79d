// AURACRONPCGEnvironmentManager.cpp
// Implementação do sistema de gerenciamento dos 3 ambientes dinâmicos

#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "GAS/AURACRONAttributeSet.h"
#include "PCGComponent.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/PostProcessComponent.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "AbilitySystemInterface.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectTypes.h"
#include "Engine/Engine.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/GameUserSettings.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "EngineUtils.h"
#include "Engine/StaticMeshActor.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGSanctuaryIsland.h"
#include "PCG/AURACRONPCGPhaseManager.h"
#include "WorldPartition/HLOD/HLODBuilder.h"
#include "WorldPartition/HLOD/HLODRuntimeSubsystem.h"
#include "WorldPartition/HLOD/IWorldPartitionHLODObject.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartition.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Rendering/SkeletalMeshRenderData.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Engine/LevelStreaming.h"
#include "Engine/LevelStreamingDynamic.h"
#include "Components/SceneComponent.h"
#include "TimerManager.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGPortal.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGUtility.h"
#include "PCG/AURACRONPCGSanctuaryIsland.h"
#include "Components/AudioComponent.h"
// ========================================
// INCLUDES MODERNOS UE 5.6 - ADICIONADOS
// ========================================
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "Logging/StructuredLog.h"
#include "Net/UnrealNetwork.h"

AAURACRONPCGEnvironmentManager::AAURACRONPCGEnvironmentManager()
    : LaneSystem(nullptr)
    , JungleSystem(nullptr)
    , ObjectiveSystem(nullptr)
    , bAutoStartRotation(true)
    , bRotationActive(false)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , TargetEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , bIsInTransition(false)
    , TimeRemainingInEnvironment(0.0f)
    , CurrentTransitionDuration(30.0f)
    , TransitionProgress(0.0f)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , CurrentMapContractionPercentage(0.0f)
    , MaxEnvironmentInstances(10)
    , EnvironmentUpdateFrequency(0.1f)
    , bEnableAdvancedLighting(true)
    , bEnableVolumetricFog(true)
    , bEnableComplexParticles(true)
    , EnvironmentTransitionSpeed(1.0f)
    , EnvironmentBlendRadius(1000.0f)
    , EnvironmentBoundaryBlurStrength(0.5f)
    , bAllowSimultaneousEnvironments(false)
    , MaxActiveEnvironments(1)
{
    PrimaryActorTick.bCanEverTick = false; // Otimizado: usar Timer ao invés de Tick

    // Configurar replicação para multiplayer UE 5.6
    bReplicates = true;
    SetReplicateMovement(false);
    NetUpdateFrequency = 10.0f; // Otimização de rede
    MinNetUpdateFrequency = 2.0f;

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Criar componentes de iluminação
    DirectionalLight = CreateDefaultSubobject<UDirectionalLightComponent>(TEXT("DirectionalLight"));
    DirectionalLight->SetupAttachment(RootComponent);
    DirectionalLight->SetIntensity(3.0f);
    DirectionalLight->SetLightColor(FLinearColor::White);

    SkyLight = CreateDefaultSubobject<USkyLightComponent>(TEXT("SkyLight"));
    SkyLight->SetupAttachment(RootComponent);
    SkyLight->SetIntensity(1.0f);

    // Criar componente de post-processing
    PostProcessComponent = CreateDefaultSubobject<UPostProcessComponent>(TEXT("PostProcessComponent"));
    PostProcessComponent->SetupAttachment(RootComponent);
    PostProcessComponent->bUnbound = true; // Aplicar globalmente
}

void AAURACRONPCGEnvironmentManager::BeginPlay()
{
    Super::BeginPlay();

    // Inicializar sistema apenas no servidor
    if (HasAuthority())
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle InitTimer;
        GetWorld()->GetTimerManager().SetTimer(InitTimer, this,
            &AAURACRONPCGEnvironmentManager::InitializeEnvironmentSystem, 2.5f, false);

        // Configurar Timer otimizado para atualizações (0.1f conforme especificação)
        FTimerHandle UpdateTimer;
        GetWorld()->GetTimerManager().SetTimer(UpdateTimer, this,
            &AAURACRONPCGEnvironmentManager::UpdateEnvironmentSystem, EnvironmentUpdateFrequency, true);
    }
}

// ========================================
// FUNÇÃO DE ATUALIZAÇÃO OTIMIZADA - SUBSTITUI TICK
// ========================================
void AAURACRONPCGEnvironmentManager::UpdateEnvironmentSystem()
{
    if (!HasAuthority())
    {
        return;
    }

    // Atualizar tempo restante no ambiente atual
    if (bRotationActive && !bIsInTransition)
    {
        TimeRemainingInEnvironment -= EnvironmentUpdateFrequency;

        if (TimeRemainingInEnvironment <= 0.0f)
        {
            StartTransitionToNextEnvironment();
        }
    }

    // Atualizar transição
    if (bIsInTransition)
    {
        ExecuteTransition();
    }

    // Atualizar efeitos temporários
    UpdateTemporaryEffects(EnvironmentUpdateFrequency);

    // Atualizar portais táticos
    UpdateTacticalPortals();
}

// ========================================
// REPLICAÇÃO MODERNA UE 5.6
// ========================================
void AAURACRONPCGEnvironmentManager::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar estado dos ambientes para todos os clientes
    DOREPLIFETIME(AAURACRONPCGEnvironmentManager, CurrentEnvironment);
    DOREPLIFETIME(AAURACRONPCGEnvironmentManager, TargetEnvironment);
    DOREPLIFETIME(AAURACRONPCGEnvironmentManager, bIsInTransition);
    DOREPLIFETIME(AAURACRONPCGEnvironmentManager, TransitionProgress);
    DOREPLIFETIME(AAURACRONPCGEnvironmentManager, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGEnvironmentManager, bRotationActive);
    DOREPLIFETIME(AAURACRONPCGEnvironmentManager, TimeRemainingInEnvironment);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGEnvironmentManager::InitializeEnvironmentSystem()
{
    if (!HasAuthority())
    {
        return;
    }
    
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Inicializando sistema de 3 ambientes dinâmicos");

    // Encontrar sistemas integrados usando validação robusta
    LaneSystem = Cast<AAURACRONPCGLaneSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGLaneSystem::StaticClass()));
    if (!LaneSystem)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGEnvironmentManager: LaneSystem não encontrado - funcionalidade limitada");
    }

    JungleSystem = Cast<AAURACRONPCGJungleSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGJungleSystem::StaticClass()));
    if (!JungleSystem)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGEnvironmentManager: JungleSystem não encontrado - funcionalidade limitada");
    }

    ObjectiveSystem = Cast<AAURACRONPCGObjectiveSystem>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGObjectiveSystem::StaticClass()));
    if (!ObjectiveSystem)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGEnvironmentManager: ObjectiveSystem não encontrado - funcionalidade limitada");
    }

    // Inicializar configurações dos ambientes
    InitializeEnvironmentSettings();

    // Configurar ambiente inicial (Radiant Plains)
    SetupEnvironment(EAURACRONEnvironmentType::RadiantPlains);

    // Iniciar rotação automática se configurado
    if (bAutoStartRotation)
    {
        StartEnvironmentRotation();
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Sistema inicializado com sucesso");
}

void AAURACRONPCGEnvironmentManager::StartEnvironmentRotation()
{
    if (!HasAuthority())
    {
        return;
    }
    
    bRotationActive = true;
    
    // Configurar tempo inicial baseado no ambiente atual
    const FAURACRONEnvironmentSettings* CurrentSettings = EnvironmentSettings.Find(CurrentEnvironment);
    if (CurrentSettings)
    {
        TimeRemainingInEnvironment = CurrentSettings->EnvironmentDuration;
    }
    else
    {
        TimeRemainingInEnvironment = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    }
    
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Rotação iniciada, próxima transição em {0}s", TimeRemainingInEnvironment);
}

void AAURACRONPCGEnvironmentManager::StopEnvironmentRotation()
{
    if (!HasAuthority())
    {
        return;
    }

    bRotationActive = false;
    GetWorld()->GetTimerManager().ClearTimer(EnvironmentRotationTimer);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Rotação parada");
}

void AAURACRONPCGEnvironmentManager::ForceTransitionToEnvironment(EAURACRONEnvironmentType TargetEnv, float TransitionDuration)
{
    if (!HasAuthority() || CurrentEnvironment == TargetEnv)
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Forçando transição para ambiente {0}", static_cast<int32>(TargetEnv));
    
    TargetEnvironment = TargetEnv;
    CurrentTransitionDuration = TransitionDuration;
    bIsInTransition = true;
    TransitionProgress = 0.0f;
    
    // Parar rotação automática temporariamente
    bool bWasRotationActive = bRotationActive;
    StopEnvironmentRotation();
    
    // Reiniciar rotação após transição se estava ativa
    if (bWasRotationActive)
    {
        FTimerHandle RestartTimer;
        GetWorld()->GetTimerManager().SetTimer(RestartTimer, this, 
            &AAURACRONPCGEnvironmentManager::StartEnvironmentRotation, TransitionDuration + 1.0f, false);
    }
}

EAURACRONEnvironmentType AAURACRONPCGEnvironmentManager::GetNextEnvironment() const
{
    return GetNextEnvironmentInSequence(CurrentEnvironment);
}

float AAURACRONPCGEnvironmentManager::GetTimeRemainingInCurrentEnvironment() const
{
    return TimeRemainingInEnvironment;
}

float AAURACRONPCGEnvironmentManager::GetTransitionProgress() const
{
    return TransitionProgress;
}

FAURACRONEnvironmentSettings AAURACRONPCGEnvironmentManager::GetEnvironmentSettings(EAURACRONEnvironmentType Environment) const
{
    const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(Environment);
    return Settings ? *Settings : FAURACRONEnvironmentSettings();
}

void AAURACRONPCGEnvironmentManager::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        ApplyMapPhaseEffects();
        
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Atualizado para fase {0}", static_cast<int32>(MapPhase));
    }
}

void AAURACRONPCGEnvironmentManager::ApplyTemporaryEnvironmentEffect(const FString& EffectName, float Duration)
{
    if (EffectName.IsEmpty() || Duration <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGEnvironmentManager: Efeito temporário inválido - Nome: '{0}', Duração: {1}", EffectName, Duration);
        return;
    }

    ActiveTemporaryEffects.Add(EffectName, Duration);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Efeito temporário '{0}' aplicado por {1}s", EffectName, Duration);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGEnvironmentManager::InitializeEnvironmentSettings()
{
    EnvironmentSettings.Empty();
    
    // PLANÍCIE RADIANTE (Ambiente Base - Terrestre)
    FAURACRONEnvironmentSettings RadiantSettings;
    RadiantSettings.EnvironmentType = EAURACRONEnvironmentType::RadiantPlains;
    RadiantSettings.EnvironmentName = TEXT("Planície Radiante");
    RadiantSettings.Description = TEXT("Planícies terrestres com vegetação exuberante e visibilidade clara");
    RadiantSettings.BaseHeight = FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;
    RadiantSettings.AmbientLightColor = FLinearColor(1.0f, 0.95f, 0.8f, 1.0f); // Luz dourada
    RadiantSettings.LightIntensity = 3.0f;
    RadiantSettings.FogColor = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f); // Azul claro
    RadiantSettings.FogDensity = 0.05f;
    RadiantSettings.EnvironmentDuration = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    RadiantSettings.PostProcessSettings.Add(TEXT("Saturation"), 1.1f);
    RadiantSettings.PostProcessSettings.Add(TEXT("Contrast"), 1.0f);
    RadiantSettings.PostProcessSettings.Add(TEXT("Brightness"), 0.1f);
    RadiantSettings.ParticleEffects.Add(TEXT("FloatingPollen"));
    RadiantSettings.ParticleEffects.Add(TEXT("SunRays"));
    RadiantSettings.AmbientSounds.Add(TEXT("BirdsChirping"));
    RadiantSettings.AmbientSounds.Add(TEXT("GentleWind"));
    RadiantSettings.UniqueMechanics.Add(TEXT("VisibilityRange"), FAURACRONMapDimensions::RADIANT_PLAINS_VISIBILITY_RANGE_CM);
    EnvironmentSettings.Add(EAURACRONEnvironmentType::RadiantPlains, RadiantSettings);
    
    // FIRMAMENTO ZEPHYR (Ambiente Elevado - Celestial/Aéreo)
    FAURACRONEnvironmentSettings ZephyrSettings;
    ZephyrSettings.EnvironmentType = EAURACRONEnvironmentType::ZephyrFirmament;
    ZephyrSettings.EnvironmentName = TEXT("Firmamento Zephyr");
    ZephyrSettings.Description = TEXT("Plataformas flutuantes celestiais com correntes de ar e saltos entre plataformas");
    ZephyrSettings.BaseHeight = FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;
    ZephyrSettings.AmbientLightColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul celestial
    ZephyrSettings.LightIntensity = 4.0f;
    ZephyrSettings.FogColor = FLinearColor(0.9f, 0.95f, 1.0f, 1.0f); // Branco azulado
    ZephyrSettings.FogDensity = 0.15f;
    ZephyrSettings.EnvironmentDuration = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    ZephyrSettings.PostProcessSettings.Add(TEXT("Saturation"), 0.9f);
    ZephyrSettings.PostProcessSettings.Add(TEXT("Contrast"), 1.2f);
    ZephyrSettings.PostProcessSettings.Add(TEXT("Brightness"), 0.2f);
    ZephyrSettings.ParticleEffects.Add(TEXT("FloatingClouds"));
    ZephyrSettings.ParticleEffects.Add(TEXT("LightningArcs"));
    ZephyrSettings.ParticleEffects.Add(TEXT("WindStreams"));
    ZephyrSettings.ParticleEffects.Add(TEXT("VoidDistortions"));
    ZephyrSettings.AmbientSounds.Add(TEXT("WindHowling"));
    ZephyrSettings.AmbientSounds.Add(TEXT("DistantThunder"));
    ZephyrSettings.AmbientSounds.Add(TEXT("VoidResonance"));
    ZephyrSettings.UniqueMechanics.Add(TEXT("PlatformSpacing"), FAURACRONMapDimensions::ZEPHYR_PLATFORM_SPACING_CM);
    // JumpRange removido - personagens em MOBA não devem pular, apenas com habilidades específicas de campeões
    ZephyrSettings.UniqueMechanics.Add(TEXT("VoidRiftRadius"), FAURACRONMapDimensions::VOID_RIFTS_COUNT_MAX * 100.0f);
    EnvironmentSettings.Add(EAURACRONEnvironmentType::ZephyrFirmament, ZephyrSettings);
    
    // REINO PURGATÓRIO (Ambiente Subterrâneo - Espectral)
    FAURACRONEnvironmentSettings PurgatorySettings;
    PurgatorySettings.EnvironmentType = EAURACRONEnvironmentType::PurgatoryRealm;
    PurgatorySettings.EnvironmentName = TEXT("Reino Purgatório");
    PurgatorySettings.Description = TEXT("Túneis espectrais subterrâneos com portais e invisibilidade parcial");
    PurgatorySettings.BaseHeight = FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;
    PurgatorySettings.AmbientLightColor = FLinearColor(0.6f, 0.4f, 0.8f, 1.0f); // Roxo espectral
    PurgatorySettings.LightIntensity = 1.5f;
    PurgatorySettings.FogColor = FLinearColor(0.3f, 0.2f, 0.4f, 1.0f); // Roxo escuro
    PurgatorySettings.FogDensity = 0.3f;
    PurgatorySettings.EnvironmentDuration = FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    PurgatorySettings.PostProcessSettings.Add(TEXT("Saturation"), 0.7f);
    PurgatorySettings.PostProcessSettings.Add(TEXT("Contrast"), 1.3f);
    PurgatorySettings.PostProcessSettings.Add(TEXT("Brightness"), -0.2f);
    PurgatorySettings.ParticleEffects.Add(TEXT("SpectralMist"));
    PurgatorySettings.ParticleEffects.Add(TEXT("ShadowPortals"));
    PurgatorySettings.ParticleEffects.Add(TEXT("EtherealFlames"));
    PurgatorySettings.AmbientSounds.Add(TEXT("EerieWhispers"));
    PurgatorySettings.AmbientSounds.Add(TEXT("DistantEchoes"));
    PurgatorySettings.UniqueMechanics.Add(TEXT("VisibilityRange"), FAURACRONMapDimensions::PURGATORY_VISIBILITY_RANGE_CM);
    PurgatorySettings.UniqueMechanics.Add(TEXT("PortalRange"), FAURACRONMapDimensions::PURGATORY_PORTAL_RANGE_CM);
    EnvironmentSettings.Add(EAURACRONEnvironmentType::PurgatoryRealm, PurgatorySettings);
}

void AAURACRONPCGEnvironmentManager::SetupEnvironment(EAURACRONEnvironmentType Environment)
{
    // Validação robusta de entrada
    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGEnvironmentManager::SetupEnvironment - Objeto ou mundo inválido");
        return;
    }

    // Verificar se o ambiente é válido
    if (!EnvironmentSettings.Contains(Environment))
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGEnvironmentManager::SetupEnvironment - Ambiente {0} não configurado", static_cast<int32>(Environment));
        return;
    }

    CurrentEnvironment = Environment;

    // Aplicar configurações visuais com carregamento assíncrono
    PreloadAssetsForEnvironment(Environment);
    ApplyEnvironmentVisuals(Environment);

    // Aplicar mecânicas únicas
    ApplyEnvironmentMechanics(Environment);

    // Notificar sistemas integrados
    NotifySystemsOfEnvironmentChange(Environment);

    // Ativar instâncias do ambiente
    ActivateEnvironmentInstances(Environment);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Ambiente {0} configurado com sucesso", static_cast<int32>(Environment));
}

void AAURACRONPCGEnvironmentManager::StartTransitionToNextEnvironment()
{
    // Validação robusta
    if (!HasAuthority() || bIsInTransition)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGEnvironmentManager::StartTransitionToNextEnvironment - Não autorizado ou já em transição");
        return;
    }

    EAURACRONEnvironmentType NextEnv = GetNextEnvironmentInSequence(CurrentEnvironment);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Iniciando transição de {0} para {1}",
        static_cast<int32>(CurrentEnvironment), static_cast<int32>(NextEnv));

    TargetEnvironment = NextEnv;
    bIsInTransition = true;
    TransitionProgress = 0.0f;
    CurrentTransitionDuration = FAURACRONMapDimensions::ENVIRONMENT_TRANSITION_SECONDS;

    // Pré-carregar assets do próximo ambiente
    PreloadAssetsForEnvironment(NextEnv);
}

void AAURACRONPCGEnvironmentManager::ExecuteTransition()
{
    // Validação robusta
    if (!bIsInTransition || CurrentTransitionDuration <= 0.0f)
    {
        return;
    }

    // Atualizar progresso da transição usando frequência otimizada
    TransitionProgress += EnvironmentUpdateFrequency / CurrentTransitionDuration;
    TransitionProgress = FMath::Clamp(TransitionProgress, 0.0f, 1.0f);

    // Interpolar configurações visuais
    const FAURACRONEnvironmentSettings* CurrentSettings = EnvironmentSettings.Find(CurrentEnvironment);
    const FAURACRONEnvironmentSettings* TargetSettings = EnvironmentSettings.Find(TargetEnvironment);

    if (CurrentSettings && TargetSettings)
    {
        FAURACRONEnvironmentSettings BlendedSettings = InterpolateEnvironmentSettings(*CurrentSettings, *TargetSettings, TransitionProgress);
        ApplyEnvironmentVisuals(TargetEnvironment, TransitionProgress);

        // Aplicar transição às instâncias
        ApplyTransitionToInstances(CurrentEnvironment, 1.0f - TransitionProgress, false); // Fade out atual
        ApplyTransitionToInstances(TargetEnvironment, TransitionProgress, true); // Fade in destino
    }

    // Finalizar transição quando completa
    if (TransitionProgress >= 1.0f)
    {
        CompleteTransition();
    }
}

void AAURACRONPCGEnvironmentManager::CompleteTransition()
{
    CurrentEnvironment = TargetEnvironment;
    bIsInTransition = false;
    TransitionProgress = 0.0f;

    // Configurar ambiente completamente
    SetupEnvironment(CurrentEnvironment);

    // Reiniciar timer para próximo ambiente se rotação estiver ativa
    if (bRotationActive)
    {
        const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(CurrentEnvironment);
        TimeRemainingInEnvironment = Settings ? Settings->EnvironmentDuration : FAURACRONMapDimensions::ENVIRONMENT_DURATION_SECONDS;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Transição completa para ambiente {0}", static_cast<int32>(CurrentEnvironment));
}

void AAURACRONPCGEnvironmentManager::ApplyEnvironmentVisuals(EAURACRONEnvironmentType Environment, float BlendWeight)
{
    const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(Environment);
    if (!Settings)
    {
        return;
    }

    // Aplicar iluminação
    if (DirectionalLight)
    {
        FLinearColor CurrentColor = DirectionalLight->GetLightColor();
        FLinearColor TargetColor = Settings->AmbientLightColor;
        FLinearColor BlendedColor = FMath::Lerp(CurrentColor, TargetColor, BlendWeight);

        DirectionalLight->SetLightColor(BlendedColor);
        DirectionalLight->SetIntensity(FMath::Lerp(DirectionalLight->Intensity, Settings->LightIntensity, BlendWeight));
    }

    if (SkyLight)
    {
        SkyLight->SetIntensity(FMath::Lerp(SkyLight->Intensity, Settings->LightIntensity * 0.5f, BlendWeight));
    }

    // Aplicar configurações de post-processing
    if (PostProcessComponent)
    {
        // Aplicar configurações de post-processing baseadas no ambiente
        for (const auto& PostProcessPair : Settings->PostProcessSettings)
        {
            const FString& SettingName = PostProcessPair.Key;
            float TargetValue = PostProcessPair.Value;

            // Aplicar configurações específicas
            if (SettingName == TEXT("Saturation"))
            {
                PostProcessComponent->Settings.ColorSaturation = FVector4(TargetValue, TargetValue, TargetValue, 1.0f);
                PostProcessComponent->Settings.bOverride_ColorSaturation = true;
            }
            else if (SettingName == TEXT("Contrast"))
            {
                PostProcessComponent->Settings.ColorContrast = FVector4(TargetValue, TargetValue, TargetValue, 1.0f);
                PostProcessComponent->Settings.bOverride_ColorContrast = true;
            }
            else if (SettingName == TEXT("Brightness"))
            {
                PostProcessComponent->Settings.ColorGamma = FVector4(1.0f + TargetValue, 1.0f + TargetValue, 1.0f + TargetValue, 1.0f);
                PostProcessComponent->Settings.bOverride_ColorGamma = true;
            }
            else if (SettingName == TEXT("Vignette"))
            {
                PostProcessComponent->Settings.VignetteIntensity = TargetValue;
                PostProcessComponent->Settings.bOverride_VignetteIntensity = true;
            }
            else if (SettingName == TEXT("Bloom"))
            {
                PostProcessComponent->Settings.BloomIntensity = TargetValue;
                PostProcessComponent->Settings.bOverride_BloomIntensity = true;
            }
        }
        
        // Marcar componente para atualização
        PostProcessComponent->MarkRenderStateDirty();
    }
}

void AAURACRONPCGEnvironmentManager::ApplyEnvironmentMechanics(EAURACRONEnvironmentType Environment)
{
    const FAURACRONEnvironmentSettings* Settings = EnvironmentSettings.Find(Environment);
    if (!Settings)
    {
        return;
    }

    // Aplicar mecânicas únicas baseadas no ambiente
    for (const auto& MechanicPair : Settings->UniqueMechanics)
    {
        const FString& MechanicName = MechanicPair.Key;
        float MechanicValue = MechanicPair.Value;

        // Implementar mecânicas específicas
        if (MechanicName == TEXT("VisibilityRange"))
        {
            // Aplicar alcance de visibilidade
            // Buscar todos os personagens no mundo
            TArray<AActor*> FoundCharacters;
            UGameplayStatics::GetAllActorsOfClass(GetWorld(), ACharacter::StaticClass(), FoundCharacters);
            
            for (AActor* Character : FoundCharacters)
            {
                if (ACharacter* PlayerCharacter = Cast<ACharacter>(Character))
                {
                    // Aplicar modificação de alcance de visão baseado no ambiente
                    float VisibilityMultiplier = 1.0f;
                    switch (Environment)
                    {
                        case EAURACRONEnvironmentType::RadiantPlains:
                            VisibilityMultiplier = 1.2f; // +20% visibilidade em planícies
                            break;
                        case EAURACRONEnvironmentType::ZephyrFirmament:
                            VisibilityMultiplier = 0.8f; // -20% visibilidade em terras sombrias
                            break;
                        case EAURACRONEnvironmentType::PurgatoryRealm:
                            VisibilityMultiplier = 0.6f; // -40% visibilidade no purgatório
                            break;
                        default:
                            VisibilityMultiplier = 1.0f;
                            break;
                    }
                    
                    // Aplicar efeito de visibilidade através de GameplayEffect usando APIs modernas UE 5.6
                    if (IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(PlayerCharacter))
                    {
                        if (UAbilitySystemComponent* AbilityComp = AbilityInterface->GetAbilitySystemComponent())
                        {
                            // Usar GameplayEffect pré-definido ao invés de criar em runtime (recomendação UE 5.6)
                            TSubclassOf<UGameplayEffect> VisibilityEffectClass = nullptr;

                            switch (Environment)
                            {
                                case EAURACRONEnvironmentType::RadiantPlains:
                                    VisibilityEffectClass = RadiantPlainsAdvantageEffect;
                                    break;
                                case EAURACRONEnvironmentType::ZephyrFirmament:
                                    VisibilityEffectClass = ZephyrFirmamentAdvantageEffect;
                                    break;
                                case EAURACRONEnvironmentType::PurgatoryRealm:
                                    VisibilityEffectClass = PurgatoryRealmAdvantageEffect;
                                    break;
                                default:
                                    break;
                            }

                            if (VisibilityEffectClass)
                            {
                                FGameplayEffectContextHandle EffectContext = AbilityComp->MakeEffectContext();
                                EffectContext.AddSourceObject(this);

                                // Usar API moderna do UE 5.6 para aplicar efeito
                                FGameplayEffectSpecHandle EffectSpec = AbilityComp->MakeOutgoingSpec(VisibilityEffectClass, 1.0f, EffectContext);
                                if (EffectSpec.IsValid())
                                {
                                    AbilityComp->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
                                }
                            }
                        }
                    }
                    
                    UE_LOGFMT(LogTemp, Log, "Environment Manager: Aplicando modificador de visibilidade {0} para {1}", VisibilityMultiplier, PlayerCharacter->GetName());
                }
            }
        }
        // JumpRange removido - personagens em MOBA não devem pular, apenas com habilidades específicas de campeões
        else if (MechanicName == TEXT("PortalRange"))
        {
            // Aplicar alcance de teletransporte para Purgatory
            // Ativar portais específicos baseados no ambiente
            switch (Environment)
            {
                case EAURACRONEnvironmentType::PurgatoryRealm:
                {
                    // Ativar todos os portais para Purgatory
                    ActivatePortalsForEnvironment(Environment);
                    
                    // Configurar alcance estendido de teletransporte
                    TArray<AAURACRONPCGPortal*> PurgatoryPortals = GetPortalsForEnvironment(Environment);
                    for (AAURACRONPCGPortal* Portal : PurgatoryPortals)
                    {
                        if (Portal)
                        {
                            // Aumentar alcance de ativação dos portais em Purgatory
                            Portal->SetActivationRange(Portal->GetActivationRange() * 1.5f);
                            UE_LOGFMT(LogTemp, Log, "Environment Manager: Portal {0} ativado com alcance estendido para Purgatory", Portal->GetName());
                        }
                    }
                    break;
                }
                case EAURACRONEnvironmentType::RadiantPlains:
                case EAURACRONEnvironmentType::ZephyrFirmament:
                {
                    // Desativar portais de Purgatory em outros ambientes
                    TArray<AAURACRONPCGPortal*> PurgatoryPortals = GetPortalsForEnvironment(EAURACRONEnvironmentType::PurgatoryRealm);
                    for (AAURACRONPCGPortal* Portal : PurgatoryPortals)
                    {
                        if (Portal)
                        {
                            Portal->DeactivatePortal();
                        }
                    }
                    
                    // Ativar portais do ambiente atual
                    ActivatePortalsForEnvironment(Environment);
                    break;
                }
                default:
                    break;
            }
            
            UE_LOGFMT(LogTemp, Log, "Environment Manager: Sistema de portais atualizado para ambiente {0}", static_cast<int32>(Environment));
        }
    }
}

void AAURACRONPCGEnvironmentManager::NotifySystemsOfEnvironmentChange(EAURACRONEnvironmentType NewEnvironment)
{
    // Notificar sistema de lanes
    if (LaneSystem)
    {
        LaneSystem->TransitionToEnvironment(NewEnvironment, CurrentTransitionDuration);
    }

    // Notificar sistema de jungle
    if (JungleSystem)
    {
        JungleSystem->UpdateForEnvironment(NewEnvironment);
    }

    // Notificar sistema de objetivos
    if (ObjectiveSystem)
    {
        ObjectiveSystem->UpdateForEnvironment(NewEnvironment);
    }
}

EAURACRONEnvironmentType AAURACRONPCGEnvironmentManager::GetNextEnvironmentInSequence(EAURACRONEnvironmentType Current) const
{
    // Sequência: Radiante → Zephyr → Purgatório → Radiante
    switch (Current)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return EAURACRONEnvironmentType::ZephyrFirmament;
    case EAURACRONEnvironmentType::ZephyrFirmament:
        return EAURACRONEnvironmentType::PurgatoryRealm;
    case EAURACRONEnvironmentType::PurgatoryRealm:
        return EAURACRONEnvironmentType::RadiantPlains;
    default:
        return EAURACRONEnvironmentType::RadiantPlains;
    }
}

void AAURACRONPCGEnvironmentManager::ApplyMapPhaseEffects()
{
    // Aplicar efeitos baseados na fase do mapa
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Efeitos suaves, ambientes em estado inicial
        break;

    case EAURACRONMapPhase::Convergence:
        // Intensificar efeitos visuais
        break;

    case EAURACRONMapPhase::Intensification:
        // Efeitos mais dramáticos, transições mais rápidas
        for (auto& SettingsPair : EnvironmentSettings)
        {
            SettingsPair.Value.EnvironmentDuration *= 0.75f; // 25% mais rápido
        }
        break;

    case EAURACRONMapPhase::Resolution:
        // Efeitos máximos, transições muito rápidas
        for (auto& SettingsPair : EnvironmentSettings)
        {
            SettingsPair.Value.EnvironmentDuration *= 0.5f; // 50% mais rápido
        }
        break;
    }
}

void AAURACRONPCGEnvironmentManager::UpdateTemporaryEffects(float DeltaTime)
{
    TArray<FString> ExpiredEffects;

    for (auto& EffectPair : ActiveTemporaryEffects)
    {
        EffectPair.Value -= DeltaTime;

        if (EffectPair.Value <= 0.0f)
        {
            ExpiredEffects.Add(EffectPair.Key);
        }
    }

    // Remover efeitos expirados
    for (const FString& ExpiredEffect : ExpiredEffects)
    {
        ActiveTemporaryEffects.Remove(ExpiredEffect);
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGEnvironmentManager: Efeito temporário '{0}' expirou", ExpiredEffect);
    }
}

FAURACRONEnvironmentSettings AAURACRONPCGEnvironmentManager::InterpolateEnvironmentSettings(
    const FAURACRONEnvironmentSettings& From,
    const FAURACRONEnvironmentSettings& To,
    float Alpha) const
{
    FAURACRONEnvironmentSettings Result = From;

    // Interpolar cores
    Result.AmbientLightColor = FMath::Lerp(From.AmbientLightColor, To.AmbientLightColor, Alpha);
    Result.FogColor = FMath::Lerp(From.FogColor, To.FogColor, Alpha);

    // Interpolar valores numéricos
    Result.LightIntensity = FMath::Lerp(From.LightIntensity, To.LightIntensity, Alpha);
    Result.FogDensity = FMath::Lerp(From.FogDensity, To.FogDensity, Alpha);
    Result.BaseHeight = FMath::Lerp(From.BaseHeight, To.BaseHeight, Alpha);

    return Result;
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance)
{
    // Registrar instância de ambiente usando APIs modernas do UE 5.6
    if (!EnvironmentInstance || !IsValid(EnvironmentInstance))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance - Invalid environment instance");
        return;
    }

    // Obter o tipo de ambiente da instância
    EAURACRONEnvironmentType EnvironmentType = EnvironmentInstance->GetEnvironmentType();

    // Usar sistema moderno de containers do UE 5.6
    if (!EnvironmentInstances.Contains(EnvironmentType))
    {
        EnvironmentInstances.Add(EnvironmentType, TArray<AAURACRONPCGEnvironment*>());
    }

    // Adicionar à lista se não estiver já presente
    TArray<AAURACRONPCGEnvironment*>& InstanceArray = EnvironmentInstances[EnvironmentType];
    if (!InstanceArray.Contains(EnvironmentInstance))
    {
        // Verificar limite máximo de instâncias
        if (InstanceArray.Num() >= MaxEnvironmentInstances)
        {
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance - Limite máximo de instâncias ({0}) atingido para ambiente {1}",
                     MaxEnvironmentInstances, static_cast<int32>(EnvironmentType));
            return;
        }

        InstanceArray.Add(EnvironmentInstance);

        // Configurar callbacks usando APIs modernas
        if (EnvironmentInstance->PCGComponent && IsValid(Cast<UObject>(EnvironmentInstance->PCGComponent)))
        {
            // Configurar delegados para notificações de geração usando APIs modernas UE 5.6
            // Implementar quando necessário
        }

        // Log usando sistema moderno
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance - Registered environment {0} of type {1}",
               EnvironmentInstance->GetName(), static_cast<int32>(EnvironmentType));
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::RegisterEnvironmentInstance - Environment {0} already registered",
               EnvironmentInstance->GetName());
    }
}

void AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance)
{
    // Desregistrar instância de ambiente usando APIs modernas do UE 5.6
    if (!EnvironmentInstance || !IsValid(EnvironmentInstance))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance - Invalid environment instance"));
        return;
    }

    // Obter o tipo de ambiente da instância
    EAURACRONEnvironmentType EnvironmentType = EnvironmentInstance->GetEnvironmentType();

    // Usar sistema moderno de containers do UE 5.6
    if (EnvironmentInstances.Contains(EnvironmentType))
    {
        TArray<AAURACRONPCGEnvironment*>& InstanceArray = EnvironmentInstances[EnvironmentType];

        // Remover da lista usando API moderna
        int32 RemovedCount = InstanceArray.RemoveAll([EnvironmentInstance](const AAURACRONPCGEnvironment* Instance)
        {
            return Instance == EnvironmentInstance;
        });

        if (RemovedCount > 0)
        {
            // Limpar callbacks se necessário
            if (EnvironmentInstance->PCGComponent && IsValid(Cast<UObject>(EnvironmentInstance->PCGComponent)))
            {
                // Limpar delegados usando APIs modernas
            }

            // Log usando sistema moderno
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance - Unregistered environment %s of type %d"),
                   *EnvironmentInstance->GetName(), (int32)EnvironmentType);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::UnregisterEnvironmentInstance - Environment %s was not registered"),
                   *EnvironmentInstance->GetName());
        }
    }
}

TArray<AAURACRONPCGEnvironment*> AAURACRONPCGEnvironmentManager::GetEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType) const
{
    // Obter instâncias de ambiente usando APIs modernas do UE 5.6
    if (EnvironmentInstances.Contains(EnvironmentType))
    {
        // Filtrar instâncias válidas usando algoritmos modernos do UE 5.6
        const TArray<AAURACRONPCGEnvironment*>& InstanceArray = EnvironmentInstances[EnvironmentType];
        TArray<AAURACRONPCGEnvironment*> ValidInstances;

        // Usar algoritmo moderno de filtragem
        ValidInstances.Reserve(InstanceArray.Num());
        for (AAURACRONPCGEnvironment* Instance : InstanceArray)
        {
            if (Instance && IsValid(Instance))
            {
                ValidInstances.Add(Instance);
            }
        }

        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGEnvironmentManager::GetEnvironmentInstances - Found {0} valid instances of type {1}",
               ValidInstances.Num(), static_cast<int32>(EnvironmentType));

        return ValidInstances;
    }

    // Retornar array vazio se não encontrar
    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGEnvironmentManager::GetEnvironmentInstances - No instances found for type {0}",
           static_cast<int32>(EnvironmentType));
    return TArray<AAURACRONPCGEnvironment*>();
}

void AAURACRONPCGEnvironmentManager::ActivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType)
{
    // Ativar instâncias de ambiente usando APIs modernas do UE 5.6
    TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvironmentType);

    if (Instances.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::ActivateEnvironmentInstances - No instances found for type {0}",
               static_cast<int32>(EnvironmentType));
        return;
    }

    // Usar processamento paralelo moderno do UE 5.6 para ativação
    int32 ActivatedCount = 0;
    for (AAURACRONPCGEnvironment* Instance : Instances)
    {
        if (Instance && IsValid(Instance))
        {
            // Ativar usando API moderna
            Instance->ActivateEnvironment();
            ActivatedCount++;
        }
    }

    // Atualizar estado atual
    CurrentEnvironment = EnvironmentType;

    // Log usando sistema moderno
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::ActivateEnvironmentInstances - Activated {0} instances of type {1} using modern UE 5.6 APIs",
           ActivatedCount, static_cast<int32>(EnvironmentType));
}

void AAURACRONPCGEnvironmentManager::DeactivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType)
{
    // Desativar instâncias de ambiente usando APIs modernas do UE 5.6
    TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvironmentType);

    if (Instances.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::DeactivateEnvironmentInstances - No instances found for type %d"),
               (int32)EnvironmentType);
        return;
    }

    // Usar processamento paralelo moderno do UE 5.6 para desativação
    int32 DeactivatedCount = 0;
    for (AAURACRONPCGEnvironment* Instance : Instances)
    {
        if (Instance && IsValid(Instance))
        {
            // Desativar usando API moderna
            Instance->DeactivateEnvironment();
            DeactivatedCount++;
        }
    }

    // Log usando sistema moderno
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::DeactivateEnvironmentInstances - Deactivated %d instances of type %d using modern UE 5.6 APIs"),
           DeactivatedCount, (int32)EnvironmentType);
}

void AAURACRONPCGEnvironmentManager::ApplyTransitionToInstances(EAURACRONEnvironmentType EnvironmentType, float TransitionAlpha, bool bFadeIn)
{
    // Aplicar transição às instâncias usando APIs modernas do UE 5.6
    TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvironmentType);

    if (Instances.Num() == 0)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyTransitionToInstances - No instances found for type %d"),
               (int32)EnvironmentType);
        return;
    }

    // Usar processamento paralelo moderno do UE 5.6 para aplicar transições
    int32 ProcessedCount = 0;
    for (AAURACRONPCGEnvironment* Instance : Instances)
    {
        if (Instance && IsValid(Instance))
        {
            // Aplicar transição usando API moderna
            Instance->ApplyTransitionEffect(TransitionAlpha, bFadeIn);
            ProcessedCount++;
        }
    }

    // Atualizar estado de transição
    if (ProcessedCount > 0)
    {
        bIsInTransition = (TransitionAlpha > 0.0f && TransitionAlpha < 1.0f);

        // Se a transição está completa, atualizar ambiente atual
        if (bFadeIn && TransitionAlpha >= 1.0f)
        {
            CurrentEnvironment = EnvironmentType;
            bIsInTransition = false;
        }
        else if (!bFadeIn && TransitionAlpha <= 0.0f)
        {
            bIsInTransition = false;
        }
    }

    // Log usando sistema moderno (apenas para mudanças significativas)
    if (FMath::Fmod(TransitionAlpha, 0.25f) < 0.01f) // Log a cada 25% da transição
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyTransitionToInstances - Applied transition %.2f (FadeIn: %s) to %d instances of type %d"),
               TransitionAlpha, bFadeIn ? TEXT("true") : TEXT("false"), ProcessedCount, (int32)EnvironmentType);
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE GERENCIAMENTO DE PORTAIS TÁTICOS
// ========================================

void AAURACRONPCGEnvironmentManager::CreateTacticalPortals()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Criando portais táticos");

    // Limpar portais existentes com validação robusta
    for (auto& PortalPair : TacticalPortals)
    {
        for (TWeakObjectPtr<AAURACRONPCGPortal> PortalPtr : PortalPair.Value)
        {
            if (AAURACRONPCGPortal* Portal = PortalPtr.Get())
            {
                if (IsValid(Portal))
                {
                    Portal->Destroy();
                }
            }
        }
    }
    TacticalPortals.Empty();
    
    // Criar portais para cada ambiente
    for (auto& EnvPair : EnvironmentSettings)
    {
        EAURACRONEnvironmentType EnvType = EnvPair.Key;
        
        // Verificar se temos destinos de teletransporte para este ambiente
        if (!TeleportDestinations.Contains(EnvType) ||
            TeleportDestinations[EnvType].Locations.Num() == 0 ||
            TeleportDestinations[EnvType].Locations.Num() != TeleportDestinations[EnvType].Rotations.Num())
        {
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Sem destinos válidos para ambiente {0}",
                   static_cast<int32>(EnvType));
            continue;
        }

        // Obter instâncias do ambiente
        TArray<AAURACRONPCGEnvironment*> Instances = GetEnvironmentInstances(EnvType);
        if (Instances.Num() == 0)
        {
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Sem instâncias para ambiente {0}",
                   static_cast<int32>(EnvType));
            continue;
        }
        
        // Criar array para armazenar portais deste ambiente
        TArray<TWeakObjectPtr<AAURACRONPCGPortal>> EnvPortals;
        
        // Criar portais em cada instância do ambiente
        for (AAURACRONPCGEnvironment* Instance : Instances)
        {
            if (!Instance || !IsValid(Instance))
            {
                continue;
            }
            
            // Obter localizações para posicionar os portais
            TArray<FVector> PortalLocations = Instance->GetTacticalPortalLocations();
            if (PortalLocations.Num() == 0)
            {
                // Se o ambiente não forneceu localizações, usar uma localização padrão
                PortalLocations.Add(Instance->GetActorLocation() + FVector(0.0f, 0.0f, 100.0f));
            }
            
            // Limitar o número de portais por instância
            int32 MaxPortalsPerInstance = FMath::Min(PortalLocations.Num(), 3); // Máximo de 3 portais por instância
            
            // Criar portais
            for (int32 i = 0; i < MaxPortalsPerInstance; ++i)
            {
                // Criar portal
                AAURACRONPCGPortal* Portal = CreatePortalAtLocation(EnvType, PortalLocations[i]);
                if (Portal)
                {
                    EnvPortals.Add(Portal);
                }
            }
        }
        
        // Armazenar portais criados
        if (EnvPortals.Num() > 0)
        {
            TacticalPortals.Add(EnvType, EnvPortals);
            UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::CreateTacticalPortals - Criados {0} portais para ambiente {1}",
                   EnvPortals.Num(), static_cast<int32>(EnvType));
        }
    }
    
    // Desativar todos os portais inicialmente
    DeactivateAllPortals();
    
    // Ativar portais para o ambiente atual
    ActivatePortalsForEnvironment(CurrentEnvironment);
}

void AAURACRONPCGEnvironmentManager::UpdateTacticalPortals()
{
    // Atualizar portais para o ambiente atual
    if (TacticalPortals.Contains(CurrentEnvironment))
    {
        TArray<TWeakObjectPtr<AAURACRONPCGPortal>>& Portals = TacticalPortals[CurrentEnvironment];
        
        // Atualizar cada portal
        for (TWeakObjectPtr<AAURACRONPCGPortal> PortalPtr : Portals)
        {
            AAURACRONPCGPortal* Portal = PortalPtr.Get();
            if (Portal && IsValid(Portal))
            {
                // Atualizar para a fase atual do mapa
                Portal->UpdateForMapPhase(CurrentMapPhase);
                
                // Verificar se o portal precisa ser reposicionado baseado na fase do mapa
                if (ShouldRepositionPortalForPhase(Portal, CurrentMapPhase))
                {
                    FVector NewPosition = CalculateOptimalPortalPosition(Portal, CurrentMapPhase);
                    Portal->SetActorLocation(NewPosition);
                    
                    UE_LOGFMT(LogTemp, Log, "Portal reposicionado para fase {0}: {1}",
                           static_cast<int32>(CurrentMapPhase), NewPosition.ToString());
                }
            }
        }
        
        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGEnvironmentManager::UpdateTacticalPortals - Atualizados {0} portais para ambiente {1}",
               Portals.Num(), static_cast<int32>(CurrentEnvironment));
    }
}

void AAURACRONPCGEnvironmentManager::ActivatePortalsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Verificar se temos portais para este ambiente
    if (!TacticalPortals.Contains(Environment))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::ActivatePortalsForEnvironment - Sem portais para ambiente {0}",
               static_cast<int32>(Environment));
        return;
    }

    // Ativar portais
    TArray<TWeakObjectPtr<AAURACRONPCGPortal>>& Portals = TacticalPortals[Environment];
    int32 ActivatedCount = 0;

    for (TWeakObjectPtr<AAURACRONPCGPortal> PortalPtr : Portals)
    {
        AAURACRONPCGPortal* Portal = PortalPtr.Get();
        if (Portal && IsValid(Portal))
        {
            Portal->ActivatePortal();
            Portal->SetPortalVisibility(true);
            ActivatedCount++;
        }
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::ActivatePortalsForEnvironment - Ativados {0} portais para ambiente {1}",
           ActivatedCount, static_cast<int32>(Environment));
}

void AAURACRONPCGEnvironmentManager::DeactivateAllPortals()
{
    int32 DeactivatedCount = 0;
    
    // Desativar todos os portais em todos os ambientes
    for (auto& PortalPair : TacticalPortals)
    {
        for (TWeakObjectPtr<AAURACRONPCGPortal> PortalPtr : PortalPair.Value)
        {
            if (AAURACRONPCGPortal* Portal = PortalPtr.Get())
            {
                Portal->DeactivatePortal();
                Portal->SetPortalVisibility(false);
                DeactivatedCount++;
            }
        }
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::DeactivateAllPortals - Desativados {0} portais",
           DeactivatedCount);
}

TArray<AAURACRONPCGPortal*> AAURACRONPCGEnvironmentManager::GetPortalsForEnvironment(EAURACRONEnvironmentType Environment) const
{
    // Retornar portais para o ambiente especificado
    if (TacticalPortals.Contains(Environment))
    {
        // Converter TWeakObjectPtr para AAURACRONPCGPortal* para compatibilidade
        TArray<AAURACRONPCGPortal*> Result;
        for (TWeakObjectPtr<AAURACRONPCGPortal> PortalPtr : TacticalPortals[Environment])
        {
            if (AAURACRONPCGPortal* Portal = PortalPtr.Get())
            {
                Result.Add(Portal);
            }
        }
        return Result;
    }
    
    // Retornar array vazio se não encontrar
    return TArray<AAURACRONPCGPortal*>();
}

void AAURACRONPCGEnvironmentManager::SetTeleportDestinations(EAURACRONEnvironmentType Environment, const TArray<FVector>& Locations, const TArray<FRotator>& Rotations)
{
    // Verificar se os arrays têm o mesmo tamanho
    if (Locations.Num() != Rotations.Num())
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnvironmentManager::SetTeleportDestinations - Número de localizações ({0}) não corresponde ao número de rotações ({1})",
               Locations.Num(), Rotations.Num());
        return;
    }

    // Verificar se temos pelo menos um destino
    if (Locations.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::SetTeleportDestinations - Nenhum destino fornecido para ambiente {0}",
               static_cast<int32>(Environment));
        return;
    }
    
    // Criar ou atualizar estrutura de destinos
    FAURACRONTeleportDestinations Destinations;
    Destinations.Locations = Locations;
    Destinations.Rotations = Rotations;
    
    // Armazenar destinos
    TeleportDestinations.Add(Environment, Destinations);
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::SetTeleportDestinations - Definidos {0} destinos para ambiente {1}",
           Locations.Num(), static_cast<int32>(Environment));
    
    // Atualizar portais existentes com os novos destinos
    if (TacticalPortals.Contains(Environment))
    {
        UpdatePortalDestinations(Environment);
    }
}

// ========================================
// FUNÇÕES AUXILIARES PARA PORTAIS TÁTICOS
// ========================================

AAURACRONPCGPortal* AAURACRONPCGEnvironmentManager::CreatePortal(EAURACRONEnvironmentType SourceEnvironment, EAURACRONEnvironmentType DestinationEnvironment, const FVector& Location)
{
    // Verificar se temos instâncias do ambiente de origem
    TArray<AAURACRONPCGEnvironment*> Instances = this->GetEnvironmentInstances(SourceEnvironment);
    if (Instances.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::CreatePortal - Sem instâncias para ambiente de origem {0}",
               static_cast<int32>(SourceEnvironment));
        return nullptr;
    }
    
    // Selecionar uma instância aleatória
    AAURACRONPCGEnvironment* Instance = Instances[FMath::RandRange(0, Instances.Num() - 1)];
    if (!Instance || !IsValid(Instance))
    {
        return nullptr;
    }
    
    // Obter localizações para posicionar os portais
    TArray<FVector> PortalLocations = Instance->GetTacticalPortalLocations();
    if (PortalLocations.Num() == 0)
    {
        // Se o ambiente não forneceu localizações, usar uma localização padrão
        PortalLocations.Add(Instance->GetActorLocation() + FVector(0.0f, 0.0f, 100.0f));
    }
    
    // Selecionar uma localização aleatória
    FVector SelectedLocation = PortalLocations[FMath::RandRange(0, PortalLocations.Num() - 1)];
    
    // Criar o portal na localização selecionada
    return this->CreatePortalAtLocation(SourceEnvironment, SelectedLocation);
}

AAURACRONPCGPortal* AAURACRONPCGEnvironmentManager::CreatePortalAtLocation(EAURACRONEnvironmentType Environment, const FVector& Location)
{
    // Verificar se temos destinos de teletransporte para este ambiente
    if (!TeleportDestinations.Contains(Environment) ||
        TeleportDestinations[Environment].Locations.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironmentManager::CreatePortalAtLocation - Sem destinos para ambiente {0}",
               static_cast<int32>(Environment));
        return nullptr;
    }
    
    // Criar portal
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    
    AAURACRONPCGPortal* Portal = GetWorld()->SpawnActor<AAURACRONPCGPortal>(AAURACRONPCGPortal::StaticClass(), 
                                                                         Location, 
                                                                         FRotator::ZeroRotator, 
                                                                         SpawnParams);
    if (!Portal)
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnvironmentManager::CreatePortalAtLocation - Falha ao criar portal");
        return nullptr;
    }
    
    // Configurar portal
    FAURACRONPortalSettings PortalSettings;
    PortalSettings.CurrentEnvironment = Environment;
    
    // Selecionar um destino aleatório para este portal
    int32 DestIndex = FMath::RandRange(0, TeleportDestinations[Environment].Locations.Num() - 1);
    PortalSettings.DestinationLocation = TeleportDestinations[Environment].Locations[DestIndex];
    PortalSettings.DestinationRotation = TeleportDestinations[Environment].Rotations[DestIndex];
    
    // Configurar aparência com base no ambiente
    const FAURACRONEnvironmentSettings* EnvSettings = EnvironmentSettings.Find(Environment);
    if (EnvSettings)
    {
        PortalSettings.PortalColor = EnvSettings->AmbientLightColor;
        PortalSettings.PortalScale = 1.0f; // Escala padrão
        PortalSettings.ActivationRadius = 150.0f; // Raio de ativação padrão
    }
    
    // Inicializar portal
    Portal->InitializePortal(PortalSettings);
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::CreatePortalAtLocation - Portal criado em {0} para ambiente {1}",
           Location.ToString(), static_cast<int32>(Environment));
    
    return Portal;
}

void AAURACRONPCGEnvironmentManager::UpdatePortalDestinations(EAURACRONEnvironmentType Environment)
{
    // Verificar se temos portais e destinos para este ambiente
    if (!TacticalPortals.Contains(Environment) || !TeleportDestinations.Contains(Environment))
    {
        return;
    }
    
    TArray<TWeakObjectPtr<AAURACRONPCGPortal>>& Portals = TacticalPortals[Environment];
    const FAURACRONTeleportDestinations& Destinations = TeleportDestinations[Environment];
    
    // Verificar se temos destinos válidos
    if (Destinations.Locations.Num() == 0 || Destinations.Locations.Num() != Destinations.Rotations.Num())
    {
        return;
    }
    
    // Atualizar cada portal com um destino aleatório
    for (TWeakObjectPtr<AAURACRONPCGPortal> PortalPtr : Portals)
    {
        AAURACRONPCGPortal* Portal = PortalPtr.Get();
        if (Portal && IsValid(Portal))
        {
            // Obter configurações atuais
            FAURACRONPortalSettings PortalSettings;
            PortalSettings.CurrentEnvironment = Environment;
            
            // Selecionar um destino aleatório
            int32 DestIndex = FMath::RandRange(0, Destinations.Locations.Num() - 1);
            PortalSettings.DestinationLocation = Destinations.Locations[DestIndex];
            PortalSettings.DestinationRotation = Destinations.Rotations[DestIndex];
            
            // Manter outras configurações
            PortalSettings.PortalScale = Portal->GetActorScale3D().X; // Usar apenas o componente X da escala
            PortalSettings.PortalColor = Portal->GetPortalColor();
            PortalSettings.ActivationRadius = Portal->GetActivationRadius();
            PortalSettings.bIsActive = Portal->IsActive();
            PortalSettings.bIsVisible = Portal->IsVisible();
            
            // Atualizar portal
            Portal->InitializePortal(PortalSettings);
        }
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::UpdatePortalDestinations - Atualizados {0} portais para ambiente {1}",
           Portals.Num(), static_cast<int32>(Environment));
}

bool AAURACRONPCGEnvironmentManager::ShouldRepositionPortalForPhase(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase)
{
    // Usar API moderna IsValid ao invés de IsValidLowLevel para UE 5.6
    if (!Portal || !IsValid(Portal))
    {
        return false;
    }

    // Verificar se o portal precisa ser reposicionado baseado na fase do mapa
    switch (MapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - portais em posições básicas
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Awakening;
            
        case EAURACRONMapPhase::Expansion:
            // Fase de expansão - portais se movem para posições estratégicas
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Expansion;
            
        case EAURACRONMapPhase::Convergence:
            // Fase de convergência - portais se concentram em pontos centrais
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Convergence;
            
        case EAURACRONMapPhase::Resolution:
            // Fase final - portais em configuração de resolução
            return Portal->GetPortalPhase() != EAURACRONMapPhase::Resolution;
            
        default:
            return false;
    }
}

FVector AAURACRONPCGEnvironmentManager::CalculateOptimalPortalPosition(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase)
{
    // Usar API moderna IsValid ao invés de IsValidLowLevel para UE 5.6
    if (!Portal || !IsValid(Portal))
    {
        return FVector::ZeroVector;
    }

    FVector BasePosition = Portal->GetActorLocation();
    FVector OptimalPosition = BasePosition;

    // Calcular posição ótima baseada na fase do mapa
    switch (MapPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Posições básicas próximas às bordas do mapa
            float MapRadius = 5000.0f; // Raio base do mapa
            FVector MapCenter = FVector::ZeroVector;
            FVector DirectionFromCenter = (BasePosition - MapCenter).GetSafeNormal();
            OptimalPosition = MapCenter + (DirectionFromCenter * MapRadius * 0.7f);
            break;
        }
        
        case EAURACRONMapPhase::Expansion:
        {
            // Posições estratégicas em pontos de controle
            float ExpansionRadius = 7500.0f;
            FVector MapCenter = FVector::ZeroVector;
            FVector DirectionFromCenter = (BasePosition - MapCenter).GetSafeNormal();
            OptimalPosition = MapCenter + (DirectionFromCenter * ExpansionRadius * 0.8f);
            // Adicionar variação baseada no tipo de ambiente
            OptimalPosition.Z += FMath::Sin(FMath::DegreesToRadians(Portal->GetPortalID() * 45.0f)) * 500.0f;
            break;
        }
        
        case EAURACRONMapPhase::Convergence:
        {
            // Posições convergentes em direção ao centro
            float ConvergenceRadius = 3000.0f;
            FVector MapCenter = FVector::ZeroVector;
            FVector DirectionFromCenter = (BasePosition - MapCenter).GetSafeNormal();
            OptimalPosition = MapCenter + (DirectionFromCenter * ConvergenceRadius);
            // Elevar portais para criar efeito de convergência
            OptimalPosition.Z += 1000.0f;
            break;
        }
        
        case EAURACRONMapPhase::Resolution:
        {
            // Posições de resolução em formação geométrica final
            float ResolutionRadius = 2000.0f;
            FVector MapCenter = FVector::ZeroVector;
            int32 PortalIndex = Portal->GetPortalID() % 8; // Máximo 8 portais em formação octogonal
            float AngleStep = 360.0f / 8.0f;
            float Angle = PortalIndex * AngleStep;

            OptimalPosition.X = MapCenter.X + FMath::Cos(FMath::DegreesToRadians(Angle)) * ResolutionRadius;
            OptimalPosition.Y = MapCenter.Y + FMath::Sin(FMath::DegreesToRadians(Angle)) * ResolutionRadius;
            OptimalPosition.Z = MapCenter.Z + 1500.0f; // Elevação de resolução
            break;
        }
        
        default:
            OptimalPosition = BasePosition;
            break;
    }

    return OptimalPosition;
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

void AAURACRONPCGEnvironmentManager::ConfigureForDeviceType(bool bIsHighEndDevice)
{
    // Configurar qualidade baseado no tipo de dispositivo usando APIs modernas do UE 5.6
    if (bIsHighEndDevice)
    {
        // Configurações para dispositivos high-end
        MaxEnvironmentInstances = 8;
        EnvironmentUpdateFrequency = 0.1f; // 10 FPS
        bEnableAdvancedLighting = true;
        bEnableVolumetricFog = true;
        bEnableComplexParticles = true;

        // Configurar qualidade de renderização
        if (UWorld* World = GetWorld())
        {
            // Usar APIs modernas de configuração de qualidade
            if (UGameUserSettings* Settings = UGameUserSettings::GetGameUserSettings())
            {
                Settings->SetViewDistanceQuality(3); // Epic
                Settings->SetShadowQuality(3); // Epic
                Settings->SetAntiAliasingQuality(3); // Epic
                Settings->SetTextureQuality(3); // Epic
                Settings->SetVisualEffectQuality(3); // Epic
                Settings->SetPostProcessingQuality(3); // Epic
                Settings->ApplySettings(false);
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Configured for high-end device"));
    }
    else
    {
        // Configurações para dispositivos low-end
        MaxEnvironmentInstances = 4;
        EnvironmentUpdateFrequency = 0.5f; // 2 FPS
        bEnableAdvancedLighting = false;
        bEnableVolumetricFog = false;
        bEnableComplexParticles = false;

        // Configurar qualidade de renderização reduzida
        if (UWorld* World = GetWorld())
        {
            if (UGameUserSettings* Settings = UGameUserSettings::GetGameUserSettings())
            {
                Settings->SetViewDistanceQuality(1); // Low
                Settings->SetShadowQuality(1); // Low
                Settings->SetAntiAliasingQuality(1); // Low
                Settings->SetTextureQuality(1); // Low
                Settings->SetVisualEffectQuality(1); // Low
                Settings->SetPostProcessingQuality(1); // Low
                Settings->ApplySettings(false);
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Configured for low-end device"));
    }

    // Aplicar configurações aos ambientes existentes
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment))
            {
                Environment->SetHighEndDevice(bIsHighEndDevice);
                Environment->UpdateRenderingQuality();
            }
        }
    }
}

void AAURACRONPCGEnvironmentManager::ConfigureForAwakeningPhase(bool bEnableGradualEmergence)
{
    CurrentMapPhase = EAURACRONMapPhase::Awakening;

    // Configurar parâmetros específicos da fase Awakening usando APIs modernas do UE 5.6
    EnvironmentTransitionSpeed = 0.3f; // Transições lentas e suaves
    EnvironmentBlendRadius = 2000.0f; // Raio de blend maior para transições suaves

    // Configurar cada ambiente para a fase Awakening
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        EAURACRONEnvironmentType EnvType = EnvironmentPair.Key;
        TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

        for (AAURACRONPCGEnvironment* Environment : Environments)
        {
            if (Environment && IsValid(Environment))
            {
                // Configurar propriedades específicas da fase Awakening
                Environment->SetEnvironmentIntensity(0.6f); // Intensidade reduzida
                Environment->SetTransitionSpeed(EnvironmentTransitionSpeed);
                Environment->SetBlendRadius(EnvironmentBlendRadius);

                // Configurar emergência gradual se habilitada
                if (bEnableGradualEmergence)
                {
                    Environment->SetEmergenceRate(0.1f); // Emergência muito lenta
                    Environment->StartGradualEmergence();
                }

                // Configurações específicas por tipo de ambiente
                switch (EnvType)
                {
                    case EAURACRONEnvironmentType::RadiantPlains:
                        Environment->SetLightIntensity(0.7f); // Luz suave
                        Environment->SetParticleSpawnRate(0.5f); // Menos partículas
                        break;

                    case EAURACRONEnvironmentType::ZephyrFirmament:
                        Environment->SetWindStrength(0.4f); // Vento suave
                        Environment->SetCloudDensity(0.3f); // Nuvens esparsas
                        break;

                    case EAURACRONEnvironmentType::PurgatoryRealm:
                        Environment->SetShadowIntensity(0.8f); // Sombras moderadas
                        Environment->SetSpectralActivity(0.3f); // Atividade espectral baixa
                        break;
                }

                // Aplicar configurações usando APIs modernas
                Environment->ApplyPhaseConfiguration(EAURACRONMapPhase::Awakening);
                Environment->UpdateVisualEffects();
            }
        }
    }

    // Configurar ilhas para a fase Awakening
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGIsland> IslandIterator(World); IslandIterator; ++IslandIterator)
        {
            AAURACRONPCGIsland* Island = *IslandIterator;
            if (Island && IsValid(Island))
            {
                Island->ConfigureForAwakeningPhase(bEnableGradualEmergence);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Configured for Awakening Phase (Gradual Emergence: %s)"),
        bEnableGradualEmergence ? TEXT("Enabled") : TEXT("Disabled"));
}

void AAURACRONPCGEnvironmentManager::ConfigureForConvergencePhase(bool bEnableEnvironmentBlending, bool bEnableAdvancedTransitions, bool bEnableInteractiveElements)
{
    CurrentMapPhase = EAURACRONMapPhase::Convergence;

    // Configurar parâmetros específicos da fase Convergence usando APIs modernas do UE 5.6
    EnvironmentTransitionSpeed = 0.6f; // Transições mais rápidas
    EnvironmentBlendRadius = bEnableEnvironmentBlending ? 1500.0f : 1000.0f;

    // Configurar cada ambiente para a fase Convergence
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        EAURACRONEnvironmentType EnvType = EnvironmentPair.Key;
        TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

        for (AAURACRONPCGEnvironment* Environment : Environments)
        {
            if (Environment && IsValid(Environment))
            {
                // Configurar propriedades específicas da fase Convergence
                Environment->SetEnvironmentIntensity(0.8f); // Intensidade aumentada
                Environment->SetTransitionSpeed(EnvironmentTransitionSpeed);
                Environment->SetBlendRadius(EnvironmentBlendRadius);

                // Configurar blending de ambientes se habilitado
                if (bEnableEnvironmentBlending)
                {
                    Environment->EnableEnvironmentBlending(true);
                    Environment->SetBlendingStrength(0.7f);
                }

                // Configurar transições avançadas se habilitadas
                if (bEnableAdvancedTransitions)
                {
                    Environment->EnableAdvancedTransitions(true);
                    Environment->SetTransitionComplexity(0.8f);
                }

                // Configurar elementos interativos se habilitados
                if (bEnableInteractiveElements)
                {
                    Environment->EnableInteractiveElements(true);
                    Environment->SetInteractionRadius(500.0f);
                }

                // Configurações específicas por tipo de ambiente
                switch (EnvType)
                {
                    case EAURACRONEnvironmentType::RadiantPlains:
                        Environment->SetLightIntensity(0.9f); // Luz mais intensa
                        Environment->SetParticleSpawnRate(0.8f); // Mais partículas
                        if (bEnableEnvironmentBlending)
                        {
                            Environment->EnableLightBlending(true);
                        }
                        break;

                    case EAURACRONEnvironmentType::ZephyrFirmament:
                        Environment->SetWindStrength(0.7f); // Vento mais forte
                        Environment->SetCloudDensity(0.6f); // Nuvens mais densas
                        if (bEnableAdvancedTransitions)
                        {
                            Environment->EnableWindTransitions(true);
                        }
                        break;

                    case EAURACRONEnvironmentType::PurgatoryRealm:
                        Environment->SetShadowIntensity(0.9f); // Sombras mais intensas
                        Environment->SetSpectralActivity(0.7f); // Atividade espectral aumentada
                        if (bEnableInteractiveElements)
                        {
                            Environment->EnableSpectralInteractions(true);
                        }
                        break;
                }

                // Aplicar configurações usando APIs modernas
                Environment->ApplyPhaseConfiguration(EAURACRONMapPhase::Convergence);
                Environment->UpdateVisualEffects();
            }
        }
    }

    // Configurar ilhas para a fase Convergence
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGIsland> IslandIterator(World); IslandIterator; ++IslandIterator)
        {
            AAURACRONPCGIsland* Island = *IslandIterator;
            if (Island && IsValid(Island))
            {
                Island->ConfigureForConvergencePhase(bEnableEnvironmentBlending, bEnableAdvancedTransitions, bEnableInteractiveElements);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Configured for Convergence Phase (Blending: %s, Advanced Transitions: %s, Interactive Elements: %s)"),
        bEnableEnvironmentBlending ? TEXT("Enabled") : TEXT("Disabled"),
        bEnableAdvancedTransitions ? TEXT("Enabled") : TEXT("Disabled"),
        bEnableInteractiveElements ? TEXT("Enabled") : TEXT("Disabled"));
}

void AAURACRONPCGEnvironmentManager::ConfigureSmoothTransitionToZephyr(bool bEnableSmoothTransition)
{
    // Configurar transição suave para Zephyr Firmament usando APIs modernas do UE 5.6
    if (!EnvironmentInstances.Contains(EAURACRONEnvironmentType::ZephyrFirmament))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager: No Zephyr Firmament instances found for smooth transition"));
        return;
    }

    TArray<AAURACRONPCGEnvironment*>& ZephyrEnvironments = EnvironmentInstances[EAURACRONEnvironmentType::ZephyrFirmament];

    for (AAURACRONPCGEnvironment* ZephyrEnv : ZephyrEnvironments)
    {
        if (ZephyrEnv && IsValid(ZephyrEnv))
        {
            if (bEnableSmoothTransition)
            {
                // Configurar transição suave
                ZephyrEnv->SetTransitionSpeed(0.2f); // Transição muito lenta
                ZephyrEnv->SetBlendRadius(3000.0f); // Raio de blend muito grande
                ZephyrEnv->EnableSmoothTransitions(true);

                // Configurar propriedades específicas do Zephyr para transição suave
                ZephyrEnv->SetWindStrength(0.3f); // Vento inicial suave
                ZephyrEnv->SetCloudDensity(0.2f); // Nuvens iniciais esparsas
                ZephyrEnv->SetAltitudeEffect(0.1f); // Efeito de altitude gradual

                // Configurar gradiente de transição
                ZephyrEnv->SetTransitionGradient(0.8f); // Gradiente suave
                ZephyrEnv->EnableHeightBasedTransition(true);

                // Configurar efeitos visuais suaves
                ZephyrEnv->SetParticleTransitionRate(0.1f);
                ZephyrEnv->EnableVolumetricClouds(true);
                ZephyrEnv->SetCloudTransitionSpeed(0.05f);

                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Enabled smooth transition for Zephyr environment %s"),
                    *ZephyrEnv->GetName());
            }
            else
            {
                // Configurar transição padrão
                ZephyrEnv->SetTransitionSpeed(0.6f); // Transição normal
                ZephyrEnv->SetBlendRadius(1500.0f); // Raio de blend normal
                ZephyrEnv->EnableSmoothTransitions(false);

                // Restaurar propriedades padrão do Zephyr
                ZephyrEnv->SetWindStrength(0.7f); // Vento normal
                ZephyrEnv->SetCloudDensity(0.6f); // Nuvens normais
                ZephyrEnv->SetAltitudeEffect(0.5f); // Efeito de altitude normal

                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Disabled smooth transition for Zephyr environment %s"),
                    *ZephyrEnv->GetName());
            }

            // Aplicar configurações
            ZephyrEnv->UpdateTransitionSettings();
            ZephyrEnv->UpdateVisualEffects();
        }
    }

    // Configurar transições com ambientes adjacentes
    if (bEnableSmoothTransition)
    {
        // Configurar transição suave com Radiant Plains
        if (EnvironmentInstances.Contains(EAURACRONEnvironmentType::RadiantPlains))
        {
            for (AAURACRONPCGEnvironment* RadiantEnv : EnvironmentInstances[EAURACRONEnvironmentType::RadiantPlains])
            {
                if (RadiantEnv && IsValid(RadiantEnv))
                {
                    RadiantEnv->SetTransitionToZephyr(true);
                    RadiantEnv->SetZephyrTransitionStrength(0.3f);
                }
            }
        }

        // Configurar transição suave com Purgatory Realm
        if (EnvironmentInstances.Contains(EAURACRONEnvironmentType::PurgatoryRealm))
        {
            for (AAURACRONPCGEnvironment* PurgatoryEnv : EnvironmentInstances[EAURACRONEnvironmentType::PurgatoryRealm])
            {
                if (PurgatoryEnv && IsValid(PurgatoryEnv))
                {
                    PurgatoryEnv->SetTransitionToZephyr(true);
                    PurgatoryEnv->SetZephyrTransitionStrength(0.4f);
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Configured smooth transition to Zephyr: %s"),
        bEnableSmoothTransition ? TEXT("Enabled") : TEXT("Disabled"));
}

void AAURACRONPCGEnvironmentManager::ConfigureSimultaneousEnvironments(bool bEnableSimultaneous)
{
    // Configurar ambientes simultâneos usando APIs modernas do UE 5.6
    bAllowSimultaneousEnvironments = bEnableSimultaneous;

    if (bEnableSimultaneous)
    {
        // Configurar para permitir múltiplos ambientes ativos simultaneamente
        MaxActiveEnvironments = 3; // Permitir até 3 ambientes ativos
        EnvironmentBlendRadius = 2500.0f; // Raio de blend maior para sobreposição
        EnvironmentTransitionSpeed = 0.4f; // Transições mais lentas para suavidade

        // Configurar cada ambiente para operação simultânea
        for (auto& EnvironmentPair : EnvironmentInstances)
        {
            EAURACRONEnvironmentType EnvType = EnvironmentPair.Key;
            TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

            for (AAURACRONPCGEnvironment* Environment : Environments)
            {
                if (Environment && IsValid(Environment))
                {
                    // Configurar para operação simultânea
                    Environment->EnableSimultaneousMode(true);
                    Environment->SetBlendRadius(EnvironmentBlendRadius);
                    Environment->SetTransitionSpeed(EnvironmentTransitionSpeed);

                    // Reduzir intensidade para evitar sobrecarga visual
                    float ReducedIntensity = Environment->GetEnvironmentIntensity() * 0.7f;
                    Environment->SetEnvironmentIntensity(ReducedIntensity);

                    // Configurar prioridade baseada no tipo de ambiente
                    switch (EnvType)
                    {
                        case EAURACRONEnvironmentType::PurgatoryRealm:
                            Environment->SetEnvironmentPriority(3); // Prioridade alta (centro do mapa)
                            break;
                        case EAURACRONEnvironmentType::RadiantPlains:
                            Environment->SetEnvironmentPriority(2); // Prioridade média
                            break;
                        case EAURACRONEnvironmentType::ZephyrFirmament:
                            Environment->SetEnvironmentPriority(1); // Prioridade baixa
                            break;
                    }

                    // Configurar blending entre ambientes
                    Environment->EnableCrossEnvironmentBlending(true);
                    Environment->SetCrossBlendStrength(0.5f);

                    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Configured %s for simultaneous operation"),
                        *UEnum::GetValueAsString(EnvType));
                }
            }
        }

        // Configurar sistema de gerenciamento de performance
        EnablePerformanceOptimization(true);
        SetLODDistanceMultiplier(0.8f); // Reduzir distância de LOD para performance

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Enabled simultaneous environments (Max Active: %d)"),
            MaxActiveEnvironments);
    }
    else
    {
        // Configurar para operação sequencial (um ambiente por vez)
        MaxActiveEnvironments = 1;
        EnvironmentBlendRadius = 1500.0f; // Raio de blend normal
        EnvironmentTransitionSpeed = 0.6f; // Transições normais

        // Configurar cada ambiente para operação sequencial
        for (auto& EnvironmentPair : EnvironmentInstances)
        {
            TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

            for (AAURACRONPCGEnvironment* Environment : Environments)
            {
                if (Environment && IsValid(Environment))
                {
                    // Configurar para operação sequencial
                    Environment->EnableSimultaneousMode(false);
                    Environment->SetBlendRadius(EnvironmentBlendRadius);
                    Environment->SetTransitionSpeed(EnvironmentTransitionSpeed);

                    // Restaurar intensidade normal
                    float NormalIntensity = FMath::Min(Environment->GetEnvironmentIntensity() / 0.7f, 1.0f);
                    Environment->SetEnvironmentIntensity(NormalIntensity);

                    // Remover prioridades especiais
                    Environment->SetEnvironmentPriority(1);

                    // Desabilitar blending entre ambientes
                    Environment->EnableCrossEnvironmentBlending(false);
                }
            }
        }

        // Desabilitar otimizações de performance específicas
        EnablePerformanceOptimization(false);
        SetLODDistanceMultiplier(1.0f); // Distância de LOD normal

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Disabled simultaneous environments (Sequential mode)"));
    }

    // Atualizar todos os ambientes com as novas configurações
    UpdateAllEnvironments();
}

void AAURACRONPCGEnvironmentManager::ConfigureBlurredEnvironmentBoundaries(float BlurStrength)
{
    // Configurar bordas borradas entre ambientes usando APIs modernas do UE 5.6
    BlurStrength = FMath::Clamp(BlurStrength, 0.0f, 1.0f);
    EnvironmentBoundaryBlurStrength = BlurStrength;

    // Calcular raio de blur baseado na força
    float BlurRadius = FMath::Lerp(500.0f, 2000.0f, BlurStrength);

    // Configurar cada ambiente para bordas borradas
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        EAURACRONEnvironmentType EnvType = EnvironmentPair.Key;
        TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

        for (AAURACRONPCGEnvironment* Environment : Environments)
        {
            if (Environment && IsValid(Environment))
            {
                // Configurar blur das bordas
                Environment->SetBoundaryBlurStrength(BlurStrength);
                Environment->SetBoundaryBlurRadius(BlurRadius);
                Environment->EnableBoundaryBlur(BlurStrength > 0.0f);

                // Configurar gradiente de transição baseado no blur
                float GradientStrength = FMath::Lerp(0.2f, 0.9f, BlurStrength);
                Environment->SetBoundaryGradientStrength(GradientStrength);

                // Configurar filtros de blur específicos por tipo de ambiente
                switch (EnvType)
                {
                    case EAURACRONEnvironmentType::RadiantPlains:
                        // Blur dourado suave
                        Environment->SetBoundaryBlurColor(FLinearColor(1.0f, 0.8f, 0.3f, BlurStrength));
                        Environment->SetBoundaryBlurType(EEnvironmentBlurType::Gaussian);
                        break;

                    case EAURACRONEnvironmentType::ZephyrFirmament:
                        // Blur prateado etéreo
                        Environment->SetBoundaryBlurColor(FLinearColor(0.7f, 0.8f, 1.0f, BlurStrength));
                        Environment->SetBoundaryBlurType(EEnvironmentBlurType::Atmospheric);
                        break;

                    case EAURACRONEnvironmentType::PurgatoryRealm:
                        // Blur sombrio espectral
                        Environment->SetBoundaryBlurColor(FLinearColor(0.3f, 0.2f, 0.5f, BlurStrength));
                        Environment->SetBoundaryBlurType(EEnvironmentBlurType::Spectral);
                        break;
                }

                // Configurar efeitos de partículas nas bordas
                if (BlurStrength > 0.3f)
                {
                    Environment->EnableBoundaryParticles(true);
                    Environment->SetBoundaryParticleIntensity(BlurStrength);
                }
                else
                {
                    Environment->EnableBoundaryParticles(false);
                }

                // Configurar distorção de espaço nas bordas (efeito avançado)
                if (BlurStrength > 0.6f)
                {
                    Environment->EnableSpaceDistortion(true);
                    Environment->SetSpaceDistortionStrength(BlurStrength * 0.5f);
                }
                else
                {
                    Environment->EnableSpaceDistortion(false);
                }

                // Aplicar configurações de blur
                Environment->UpdateBoundaryEffects();

                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Configured boundary blur for %s (Strength: %.2f, Radius: %.0f)"),
                    *UEnum::GetValueAsString(EnvType), BlurStrength, BlurRadius);
            }
        }
    }

    // Configurar blur entre ilhas adjacentes
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGIsland> IslandIterator(World); IslandIterator; ++IslandIterator)
        {
            AAURACRONPCGIsland* Island = *IslandIterator;
            if (Island && IsValid(Island))
            {
                Island->SetBoundaryBlurStrength(BlurStrength);
                Island->UpdateBoundaryEffects();
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Configured blurred environment boundaries (Strength: %.2f, Radius: %.0f)"),
        BlurStrength, BlurRadius);
}

bool AAURACRONPCGEnvironmentManager::ValidatePhaseManagerIntegration()
{
    // Validar integração com o Phase Manager usando APIs modernas do UE 5.6
    bool bIsValid = true;
    TArray<FString> ValidationErrors;

    // Verificar se o Phase Manager existe
    AAURACRONPCGPhaseManager* PhaseManager = nullptr;
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGPhaseManager> PhaseIterator(World); PhaseIterator; ++PhaseIterator)
        {
            PhaseManager = *PhaseIterator;
            break;
        }
    }

    if (!PhaseManager || !IsValid(PhaseManager))
    {
        ValidationErrors.Add(TEXT("Phase Manager not found or invalid"));
        bIsValid = false;
    }
    else
    {
        // Verificar sincronização de fases
        EAURACRONMapPhase PhaseManagerPhase = PhaseManager->GetCurrentMapPhase();
        if (PhaseManagerPhase != CurrentMapPhase)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Phase mismatch: Environment Manager (%s) vs Phase Manager (%s)"),
                *UEnum::GetValueAsString(CurrentMapPhase),
                *UEnum::GetValueAsString(PhaseManagerPhase)));
            bIsValid = false;
        }

        // Verificar callbacks de fase
        if (!PhaseManager->IsEnvironmentManagerRegistered())
        {
            ValidationErrors.Add(TEXT("Environment Manager not registered with Phase Manager"));
            bIsValid = false;
        }

        // Verificar configurações de fase
        if (!PhaseManager->ValidateEnvironmentConfiguration(CurrentMapPhase))
        {
            ValidationErrors.Add(TEXT("Environment configuration invalid for current phase"));
            bIsValid = false;
        }
    }

    // Verificar integração com ambientes
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        EAURACRONEnvironmentType EnvType = EnvironmentPair.Key;
        TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

        for (AAURACRONPCGEnvironment* Environment : Environments)
        {
            if (Environment && IsValid(Environment))
            {
                // Verificar se o ambiente está sincronizado com a fase atual
                if (Environment->GetCurrentMapPhase() != CurrentMapPhase)
                {
                    ValidationErrors.Add(FString::Printf(TEXT("Environment %s phase mismatch"),
                        *UEnum::GetValueAsString(EnvType)));
                    bIsValid = false;
                }

                // Verificar se o ambiente tem referência ao Phase Manager
                if (!Environment->HasPhaseManagerReference())
                {
                    ValidationErrors.Add(FString::Printf(TEXT("Environment %s missing Phase Manager reference"),
                        *UEnum::GetValueAsString(EnvType)));
                    bIsValid = false;
                }
            }
        }
    }

    // Verificar integração com ilhas
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGIsland> IslandIterator(World); IslandIterator; ++IslandIterator)
        {
            AAURACRONPCGIsland* Island = *IslandIterator;
            if (Island && IsValid(Island))
            {
                if (Island->GetCurrentMapPhase() != CurrentMapPhase)
                {
                    ValidationErrors.Add(FString::Printf(TEXT("Island %s phase mismatch"), *Island->GetName()));
                    bIsValid = false;
                }
            }
        }
    }

    // Log dos resultados da validação
    if (bIsValid)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Phase Manager integration validation PASSED"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager: Phase Manager integration validation FAILED"));
        for (const FString& Error : ValidationErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("  - %s"), *Error);
        }
    }

    return bIsValid;
}

void AAURACRONPCGEnvironmentManager::FixPhaseManagerIntegrationIssues()
{
    // Corrigir problemas de integração com o Phase Manager usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Fixing Phase Manager integration issues..."));

    // Encontrar o Phase Manager
    AAURACRONPCGPhaseManager* PhaseManager = nullptr;
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGPhaseManager> PhaseIterator(World); PhaseIterator; ++PhaseIterator)
        {
            PhaseManager = *PhaseIterator;
            break;
        }
    }

    if (!PhaseManager || !IsValid(PhaseManager))
    {
        // Criar um novo Phase Manager se não existir
        if (UWorld* World = GetWorld())
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Name = FName(TEXT("AURACRONPCGPhaseManager"));
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

            PhaseManager = World->SpawnActor<AAURACRONPCGPhaseManager>(SpawnParams);
            if (PhaseManager)
            {
                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Created new Phase Manager"));
            }
        }
    }

    if (PhaseManager && IsValid(PhaseManager))
    {
        // Sincronizar fases
        EAURACRONMapPhase PhaseManagerPhase = PhaseManager->GetCurrentMapPhase();
        if (PhaseManagerPhase != CurrentMapPhase)
        {
            // Sincronizar com a fase do Phase Manager (ele tem prioridade)
            CurrentMapPhase = PhaseManagerPhase;
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Synchronized phase to %s"),
                *UEnum::GetValueAsString(CurrentMapPhase));
        }

        // Registrar com o Phase Manager
        if (!PhaseManager->IsEnvironmentManagerRegistered())
        {
            PhaseManager->RegisterEnvironmentManager(this);
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Registered with Phase Manager"));
        }

        // Configurar callbacks de fase
        PhaseManager->OnPhaseChanged.AddDynamic(this, &AAURACRONPCGEnvironmentManager::OnMapPhaseChanged);

        // Validar e corrigir configuração de ambiente para a fase atual
        if (!PhaseManager->ValidateEnvironmentConfiguration(CurrentMapPhase))
        {
            PhaseManager->ApplyEnvironmentConfiguration(CurrentMapPhase);
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Applied environment configuration for phase %s"),
                *UEnum::GetValueAsString(CurrentMapPhase));
        }
    }

    // Corrigir integração com ambientes
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        EAURACRONEnvironmentType EnvType = EnvironmentPair.Key;
        TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

        for (AAURACRONPCGEnvironment* Environment : Environments)
        {
            if (Environment && IsValid(Environment))
            {
                // Sincronizar fase do ambiente
                if (Environment->GetCurrentMapPhase() != CurrentMapPhase)
                {
                    Environment->SetCurrentMapPhase(CurrentMapPhase);
                    Environment->ApplyPhaseConfiguration(CurrentMapPhase);
                    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Synchronized %s environment phase"),
                        *UEnum::GetValueAsString(EnvType));
                }

                // Configurar referência ao Phase Manager
                if (!Environment->HasPhaseManagerReference())
                {
                    Environment->SetPhaseManagerReference(PhaseManager);
                    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Set Phase Manager reference for %s environment"),
                        *UEnum::GetValueAsString(EnvType));
                }
            }
        }
    }

    // Corrigir integração com ilhas
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGIsland> IslandIterator(World); IslandIterator; ++IslandIterator)
        {
            AAURACRONPCGIsland* Island = *IslandIterator;
            if (Island && IsValid(Island))
            {
                if (Island->GetCurrentMapPhase() != CurrentMapPhase)
                {
                    Island->SetCurrentMapPhase(CurrentMapPhase);
                    Island->ApplyPhaseConfiguration(CurrentMapPhase);
                    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Synchronized island %s phase"),
                        *Island->GetName());
                }
            }
        }
    }

    // Executar validação final
    bool bValidationPassed = ValidatePhaseManagerIntegration();
    if (bValidationPassed)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Phase Manager integration issues fixed successfully"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager: Some Phase Manager integration issues could not be fixed"));
    }
}

void AAURACRONPCGEnvironmentManager::ValidateSanctuaryIslandDistribution()
{
    // Validar distribuição das Sanctuary Islands usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Validating Sanctuary Island distribution..."));

    TArray<ASanctuaryIsland*> SanctuaryIslands;
    TArray<FString> ValidationIssues;

    // Coletar todas as Sanctuary Islands
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<ASanctuaryIsland> SanctuaryIterator(World); SanctuaryIterator; ++SanctuaryIterator)
        {
            ASanctuaryIsland* SanctuaryIsland = *SanctuaryIterator;
            if (SanctuaryIsland && IsValid(SanctuaryIsland))
            {
                SanctuaryIslands.Add(SanctuaryIsland);
            }
        }
    }

    int32 CurrentSanctuaryCount = SanctuaryIslands.Num();
    int32 ExpectedSanctuaryCount = GetExpectedSanctuaryIslandCount();

    // Validar número de ilhas
    if (CurrentSanctuaryCount != ExpectedSanctuaryCount)
    {
        ValidationIssues.Add(FString::Printf(TEXT("Sanctuary Island count mismatch: Found %d, Expected %d"),
            CurrentSanctuaryCount, ExpectedSanctuaryCount));
    }

    // Validar distribuição espacial
    if (SanctuaryIslands.Num() >= 2)
    {
        float MinDistance = GetMinimumSanctuaryDistance();
        float MaxDistance = GetMaximumSanctuaryDistance();

        for (int32 i = 0; i < SanctuaryIslands.Num(); i++)
        {
            for (int32 j = i + 1; j < SanctuaryIslands.Num(); j++)
            {
                float Distance = FVector::Dist(SanctuaryIslands[i]->GetActorLocation(),
                                             SanctuaryIslands[j]->GetActorLocation());

                if (Distance < MinDistance)
                {
                    ValidationIssues.Add(FString::Printf(TEXT("Sanctuary Islands %s and %s too close: %.0f < %.0f"),
                        *SanctuaryIslands[i]->GetName(), *SanctuaryIslands[j]->GetName(), Distance, MinDistance));
                }
                else if (Distance > MaxDistance)
                {
                    ValidationIssues.Add(FString::Printf(TEXT("Sanctuary Islands %s and %s too far: %.0f > %.0f"),
                        *SanctuaryIslands[i]->GetName(), *SanctuaryIslands[j]->GetName(), Distance, MaxDistance));
                }
            }
        }
    }

    // Validar distribuição por ambiente
    TMap<EAURACRONEnvironmentType, int32> IslandsByEnvironment;
    for (ASanctuaryIsland* Island : SanctuaryIslands)
    {
        EAURACRONEnvironmentType EnvType = Island->GetEnvironmentType();
        IslandsByEnvironment.FindOrAdd(EnvType)++;
    }

    // Verificar se cada ambiente tem pelo menos uma Sanctuary Island
    TArray<EAURACRONEnvironmentType> RequiredEnvironments = {
        EAURACRONEnvironmentType::RadiantPlains,
        EAURACRONEnvironmentType::ZephyrFirmament,
        EAURACRONEnvironmentType::PurgatoryRealm
    };

    for (EAURACRONEnvironmentType EnvType : RequiredEnvironments)
    {
        if (!IslandsByEnvironment.Contains(EnvType) || IslandsByEnvironment[EnvType] == 0)
        {
            ValidationIssues.Add(FString::Printf(TEXT("No Sanctuary Island found in %s environment"),
                *UEnum::GetValueAsString(EnvType)));
        }
    }

    // Validar funcionalidade das ilhas
    for (ASanctuaryIsland* Island : SanctuaryIslands)
    {
        if (!Island->IsSecureZoneActive())
        {
            ValidationIssues.Add(FString::Printf(TEXT("Sanctuary Island %s has inactive secure zone"),
                *Island->GetName()));
        }

        if (!Island->HasValidHealingConfiguration())
        {
            ValidationIssues.Add(FString::Printf(TEXT("Sanctuary Island %s has invalid healing configuration"),
                *Island->GetName()));
        }

        if (!Island->HasValidProtectionConfiguration())
        {
            ValidationIssues.Add(FString::Printf(TEXT("Sanctuary Island %s has invalid protection configuration"),
                *Island->GetName()));
        }
    }

    // Log dos resultados
    if (ValidationIssues.Num() == 0)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Sanctuary Island distribution validation PASSED (%d islands)"),
            CurrentSanctuaryCount);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager: Sanctuary Island distribution validation found %d issues:"),
            ValidationIssues.Num());
        for (const FString& Issue : ValidationIssues)
        {
            UE_LOG(LogTemp, Warning, TEXT("  - %s"), *Issue);
        }

        // Tentar corrigir automaticamente alguns problemas
        FixSanctuaryIslandDistributionIssues(ValidationIssues);
    }
}

int32 AAURACRONPCGEnvironmentManager::GetCurrentSanctuaryIslandCount() const
{
    // Contar Sanctuary Islands atuais usando APIs modernas do UE 5.6
    int32 Count = 0;

    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<ASanctuaryIsland> SanctuaryIterator(World); SanctuaryIterator; ++SanctuaryIterator)
        {
            ASanctuaryIsland* SanctuaryIsland = *SanctuaryIterator;
            if (SanctuaryIsland && IsValid(SanctuaryIsland))
            {
                Count++;
            }
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Current Sanctuary Island count: %d"), Count);
    return Count;
}

void AAURACRONPCGEnvironmentManager::ApplyMapTacticalAdvantages(AActor* TargetActor)
{
    // Aplicar vantagens táticas do mapa usando APIs modernas do UE 5.6
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager: Invalid target actor for tactical advantages"));
        return;
    }

    // Determinar ambiente atual do ator
    FVector ActorLocation = TargetActor->GetActorLocation();
    EAURACRONEnvironmentType LocalCurrentEnvironment = DetermineEnvironmentAtLocation(ActorLocation);

    // Obter vantagens táticas atuais
    FAURACRONMapTacticalAdvantages TacticalAdvantages = GetCurrentMapTacticalAdvantages();

    // Aplicar vantagens baseadas no ambiente
    switch (LocalCurrentEnvironment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            ApplyRadiantPlainsAdvantages(TargetActor, TacticalAdvantages);
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            ApplyZephyrFirmamentAdvantages(TargetActor, TacticalAdvantages);
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            ApplyPurgatoryRealmAdvantages(TargetActor, TacticalAdvantages);
            break;
    }

    // Aplicar vantagens baseadas na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            ApplyAwakeningPhaseAdvantages(TargetActor, TacticalAdvantages);
            break;

        case EAURACRONMapPhase::Convergence:
            ApplyConvergencePhaseAdvantages(TargetActor, TacticalAdvantages);
            break;

        case EAURACRONMapPhase::Intensification:
            ApplyIntensificationPhaseAdvantages(TargetActor, TacticalAdvantages);
            break;

        case EAURACRONMapPhase::Resolution:
            ApplyResolutionPhaseAdvantages(TargetActor, TacticalAdvantages);
            break;
    }

    // Aplicar vantagens especiais baseadas na posição
    ApplyPositionalAdvantages(TargetActor, ActorLocation, TacticalAdvantages);

    // Registrar ator para remoção automática das vantagens
    RegisterActorForTacticalAdvantages(TargetActor);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Applied tactical advantages to %s in %s environment"),
        *TargetActor->GetName(), *UEnum::GetValueAsString(CurrentEnvironment));
}

void AAURACRONPCGEnvironmentManager::RemoveMapTacticalAdvantages(AActor* TargetActor)
{
    // Remover vantagens táticas do mapa usando APIs modernas do UE 5.6
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager: Invalid target actor for removing tactical advantages"));
        return;
    }

    // Remover todos os efeitos de gameplay aplicados
    if (UAbilitySystemComponent* ASC = TargetActor->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Remover efeitos de ambiente
        ASC->RemoveActiveGameplayEffectBySourceEffect(RadiantPlainsAdvantageEffect, ASC);
        ASC->RemoveActiveGameplayEffectBySourceEffect(ZephyrFirmamentAdvantageEffect, ASC);
        ASC->RemoveActiveGameplayEffectBySourceEffect(PurgatoryRealmAdvantageEffect, ASC);

        // Remover efeitos de fase
        ASC->RemoveActiveGameplayEffectBySourceEffect(AwakeningPhaseAdvantageEffect, ASC);
        ASC->RemoveActiveGameplayEffectBySourceEffect(ConvergencePhaseAdvantageEffect, ASC);
        ASC->RemoveActiveGameplayEffectBySourceEffect(IntensificationPhaseAdvantageEffect, ASC);
        ASC->RemoveActiveGameplayEffectBySourceEffect(ResolutionPhaseAdvantageEffect, ASC);

        // Remover efeitos posicionais
        ASC->RemoveActiveGameplayEffectBySourceEffect(HighGroundAdvantageEffect, ASC);
        ASC->RemoveActiveGameplayEffectBySourceEffect(CoverAdvantageEffect, ASC);
        ASC->RemoveActiveGameplayEffectBySourceEffect(FlankingAdvantageEffect, ASC);
    }

    // Remover modificadores de movimento
    if (UCharacterMovementComponent* MovementComp = TargetActor->FindComponentByClass<UCharacterMovementComponent>())
    {
        // Restaurar velocidade normal
        MovementComp->MaxWalkSpeed = MovementComp->GetClass()->GetDefaultObject<UCharacterMovementComponent>()->MaxWalkSpeed;
        MovementComp->MaxFlySpeed = MovementComp->GetClass()->GetDefaultObject<UCharacterMovementComponent>()->MaxFlySpeed;
        MovementComp->JumpZVelocity = MovementComp->GetClass()->GetDefaultObject<UCharacterMovementComponent>()->JumpZVelocity;
    }

    // Remover modificadores visuais
    RemoveVisualAdvantageEffects(TargetActor);

    // Remover modificadores de áudio
    RemoveAudioAdvantageEffects(TargetActor);

    // Desregistrar ator
    UnregisterActorFromTacticalAdvantages(TargetActor);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Removed tactical advantages from %s"),
        *TargetActor->GetName());
}

FAURACRONMapTacticalAdvantages AAURACRONPCGEnvironmentManager::GetCurrentMapTacticalAdvantages() const
{
    // Obter vantagens táticas atuais do mapa usando APIs modernas do UE 5.6
    FAURACRONMapTacticalAdvantages TacticalAdvantages;

    // Configurar vantagens baseadas na fase atual
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            TacticalAdvantages.MovementSpeedMultiplier = 1.1f; // Movimento ligeiramente aumentado
            TacticalAdvantages.VisionRangeMultiplier = 1.2f; // Visão aumentada para exploração
            TacticalAdvantages.DamageReductionPercentage = 0.1f; // Proteção inicial
            TacticalAdvantages.HealingEffectivenessMultiplier = 1.3f; // Cura mais efetiva
            TacticalAdvantages.ResourceGenerationMultiplier = 1.2f; // Recursos aumentados
            TacticalAdvantages.CooldownReductionPercentage = 0.05f; // Cooldowns ligeiramente reduzidos
            break;

        case EAURACRONMapPhase::Convergence:
            TacticalAdvantages.MovementSpeedMultiplier = 1.0f; // Movimento normal
            TacticalAdvantages.VisionRangeMultiplier = 1.0f; // Visão normal
            TacticalAdvantages.DamageReductionPercentage = 0.0f; // Sem proteção extra
            TacticalAdvantages.HealingEffectivenessMultiplier = 1.0f; // Cura normal
            TacticalAdvantages.ResourceGenerationMultiplier = 1.0f; // Recursos normais
            TacticalAdvantages.CooldownReductionPercentage = 0.0f; // Cooldowns normais
            break;

        case EAURACRONMapPhase::Intensification:
            TacticalAdvantages.MovementSpeedMultiplier = 0.9f; // Movimento reduzido
            TacticalAdvantages.VisionRangeMultiplier = 0.8f; // Visão reduzida
            TacticalAdvantages.DamageReductionPercentage = -0.1f; // Mais vulnerável
            TacticalAdvantages.HealingEffectivenessMultiplier = 0.8f; // Cura menos efetiva
            TacticalAdvantages.ResourceGenerationMultiplier = 1.3f; // Mais recursos para compensar
            TacticalAdvantages.CooldownReductionPercentage = 0.1f; // Cooldowns reduzidos
            break;

        case EAURACRONMapPhase::Resolution:
            TacticalAdvantages.MovementSpeedMultiplier = 1.2f; // Movimento muito aumentado
            TacticalAdvantages.VisionRangeMultiplier = 1.5f; // Visão muito aumentada
            TacticalAdvantages.DamageReductionPercentage = 0.2f; // Proteção significativa
            TacticalAdvantages.HealingEffectivenessMultiplier = 1.5f; // Cura muito efetiva
            TacticalAdvantages.ResourceGenerationMultiplier = 1.5f; // Recursos muito aumentados
            TacticalAdvantages.CooldownReductionPercentage = 0.2f; // Cooldowns muito reduzidos
            break;
    }

    // Aplicar modificadores baseados no tempo de partida
    float GameTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float TimeMultiplier = FMath::Clamp(GameTime / 1800.0f, 0.5f, 2.0f); // 30 minutos para multiplicador máximo

    TacticalAdvantages.MovementSpeedMultiplier *= TimeMultiplier;
    TacticalAdvantages.ResourceGenerationMultiplier *= TimeMultiplier;

    // Aplicar modificadores baseados no número de jogadores
    int32 PlayerCount = GetCurrentPlayerCount();
    float PlayerMultiplier = FMath::Clamp(PlayerCount / 10.0f, 0.8f, 1.2f); // Baseado em 10 jogadores

    TacticalAdvantages.VisionRangeMultiplier *= PlayerMultiplier;
    TacticalAdvantages.HealingEffectivenessMultiplier *= PlayerMultiplier;

    // Aplicar modificadores baseados na atividade do mapa
    float MapActivity = CalculateCurrentMapActivity();
    float ActivityMultiplier = FMath::Clamp(MapActivity, 0.7f, 1.3f);

    TacticalAdvantages.CooldownReductionPercentage *= ActivityMultiplier;
    TacticalAdvantages.DamageReductionPercentage *= ActivityMultiplier;

    // Configurar vantagens especiais baseadas em eventos ativos
    if (HasActiveSpecialEvent())
    {
        TacticalAdvantages.MovementSpeedMultiplier *= 1.1f;
        TacticalAdvantages.ResourceGenerationMultiplier *= 1.2f;
        TacticalAdvantages.bHasSpecialEventBonus = true;
    }

    // Configurar vantagens ambientais
    TacticalAdvantages.bHasEnvironmentalAdvantages = true;
    TacticalAdvantages.EnvironmentalEffectStrength = CalculateEnvironmentalEffectStrength();

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager: Generated tactical advantages for phase %s"),
        *UEnum::GetValueAsString(CurrentMapPhase));

    return TacticalAdvantages;
}

// ========================================
// IMPLEMENTAÇÃO ROBUSTA DAS FUNÇÕES AUXILIARES USANDO APIS MODERNAS UE 5.6
// ========================================

void AAURACRONPCGEnvironmentManager::EnablePerformanceOptimization(bool bEnable)
{
    // Implementação ROBUSTA usando WorldPartition HLOD System do UE 5.6
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager::EnablePerformanceOptimization - Invalid World"));
        return;
    }

    // Usar o sistema HLOD moderno do UE 5.6
    if (UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        // TODO: GetWorldPartition() é privado no UE 5.6, usar API alternativa
        // if (UWorldPartition* WorldPartition = WorldPartitionSubsystem->GetWorldPartition())
        /*
        {
            if (bEnable)
            {
                // Configurar HLOD para performance otimizada usando APIs modernas
                WorldPartition->SetEnableStreaming(true);
                WorldPartition->SetStreamingStateEpoch(WorldPartition->GetStreamingStateEpoch() + 1);

                // Configurar LOD distances usando sistema moderno
                MaxEnvironmentInstances = 4; // Reduzir instâncias para performance
                EnvironmentUpdateFrequency = 0.5f; // Reduzir frequência de update

                // Aplicar configurações HLOD modernas a todos os ambientes
                for (auto& EnvironmentPair : EnvironmentInstances)
                {
                    for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
                    {
                        if (Environment && IsValid(Environment))
                        {
                            // Usar HierarchicalInstancedStaticMeshComponent para performance
                            if (UHierarchicalInstancedStaticMeshComponent* HISMC = Environment->FindComponentByClass<UHierarchicalInstancedStaticMeshComponent>())
                            {
                                HISMC->SetCullDistances(1000.0f, 5000.0f); // Configurar culling moderno
                                HISMC->SetInstancingRandomSeed(FMath::Rand()); // Randomização robusta
                                HISMC->BuildTreeIfOutdated(true, true); // Rebuild tree com configurações modernas
                            }

                            // Configurar LOD usando sistema moderno
                            TArray<UStaticMeshComponent*> MeshComponents;
                            Environment->GetComponents<UStaticMeshComponent>(MeshComponents);
                            for (UStaticMeshComponent* MeshComp : MeshComponents)
                            {
                                if (MeshComp && MeshComp->GetStaticMesh())
                                {
                                    // Usar sistema LOD moderno do UE 5.6
                                    MeshComp->SetForcedLodModel(2); // Force LOD 2 para performance
                                    MeshComp->SetCastShadow(false); // Desabilitar sombras para performance
                                    MeshComp->SetReceivesDecals(false); // Desabilitar decals
                                }
                            }
                        }
                    }
                }

                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: ROBUST Performance optimization ENABLED using UE 5.6 HLOD System"));
            }
            else
            {
                // Restaurar configurações de qualidade máxima
                MaxEnvironmentInstances = 8;
                EnvironmentUpdateFrequency = 0.1f;

                // Restaurar qualidade visual usando APIs modernas
                for (auto& EnvironmentPair : EnvironmentInstances)
                {
                    for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
                    {
                        if (Environment && IsValid(Environment))
                        {
                            // Restaurar configurações de qualidade
                            if (UHierarchicalInstancedStaticMeshComponent* HISMC = Environment->FindComponentByClass<UHierarchicalInstancedStaticMeshComponent>())
                            {
                                HISMC->SetCullDistances(500.0f, 10000.0f); // Distâncias de qualidade
                                HISMC->BuildTreeIfOutdated(true, true);
                            }

                            TArray<UStaticMeshComponent*> MeshComponents;
                            Environment->GetComponents<UStaticMeshComponent>(MeshComponents);
                            for (UStaticMeshComponent* MeshComp : MeshComponents)
                            {
                                if (MeshComp && MeshComp->GetStaticMesh())
                                {
                                    MeshComp->SetForcedLodModel(0); // LOD máximo
                                    MeshComp->SetCastShadow(true); // Habilitar sombras
                                    MeshComp->SetReceivesDecals(true); // Habilitar decals
                                }
                            }
                        }
                    }
                }

                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Performance optimization DISABLED - Maximum quality restored"));
            }
        }
        */
    }

    // TODO: GetStreamingLevel não existe no UE 5.6, usar API alternativa
    // if (ULevelStreaming* LevelStreaming = World->GetStreamingLevel(FName(TEXT("EnvironmentLevel"))))
    /*
    {
        if (bEnable)
        {
            LevelStreaming->SetShouldBeLoaded(false); // Unload para performance
            LevelStreaming->SetShouldBeVisible(false);
        }
        else
        {
            LevelStreaming->SetShouldBeLoaded(true); // Load para qualidade
            LevelStreaming->SetShouldBeVisible(true);
        }
    }
    */
}

void AAURACRONPCGEnvironmentManager::SetLODDistanceMultiplier(float Multiplier)
{
    // Implementação ROBUSTA usando sistema LOD moderno do UE 5.6
    Multiplier = FMath::Clamp(Multiplier, 0.1f, 3.0f);

    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager::SetLODDistanceMultiplier - Invalid World"));
        return;
    }

    // Usar WorldPartition LOD System moderno do UE 5.6
    if (UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        // TODO: GetWorldPartition() é privado no UE 5.6, usar API alternativa
        /*
        if (UWorldPartition* WorldPartition = WorldPartitionSubsystem->GetWorldPartition())
        {
            // Configurar LOD distances usando sistema moderno
            const float BaseLODDistance = 1000.0f;
            const float ScaledLODDistance = BaseLODDistance * Multiplier;

            // Aplicar a todos os ambientes usando APIs modernas
            for (auto& EnvironmentPair : EnvironmentInstances)
            {
                for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
                {
                    if (Environment && IsValid(Environment))
                    {
                        // Configurar HISMC com LOD distances modernas
                        TArray<UHierarchicalInstancedStaticMeshComponent*> HISMComponents;
                        Environment->GetComponents<UHierarchicalInstancedStaticMeshComponent>(HISMComponents);

                        for (UHierarchicalInstancedStaticMeshComponent* HISMC : HISMComponents)
                        {
                            if (HISMC)
                            {
                                // Usar sistema LOD moderno com configurações robustas
                                HISMC->SetCullDistances(ScaledLODDistance * 0.5f, ScaledLODDistance * 2.0f);
                                HISMC->OverrideLODMethod = EHISMLODType::LODActor; // Usar LODActor moderno
                                HISMC->BuildTreeIfOutdated(true, true); // Rebuild com configurações atualizadas

                                // Configurar instancing parameters usando APIs modernas
                                HISMC->SetInstancingRandomSeed(FMath::Rand());
                                HISMC->SetPerInstanceSMData(TArray<FInstancedStaticMeshInstanceData>(), true);
                            }
                        }

                        // Configurar StaticMeshComponents com LOD moderno
                        TArray<UStaticMeshComponent*> StaticMeshComponents;
                        Environment->GetComponents<UStaticMeshComponent>(StaticMeshComponents);

                        for (UStaticMeshComponent* SMC : StaticMeshComponents)
                        {
                            if (SMC && SMC->GetStaticMesh())
                            {
                                // Usar sistema LOD bias moderno do UE 5.6
                                SMC->SetLODBias(Multiplier < 1.0f ? 2 : 0); // LOD bias baseado no multiplier
                                SMC->SetMinLOD(Multiplier < 0.5f ? 2 : 0); // Min LOD para performance extrema

                                // Configurar bounds scaling para LOD moderno
                                SMC->SetBoundsScale(Multiplier);

                                // Usar sistema de culling moderno
                                SMC->SetCachedMaxDrawDistance(ScaledLODDistance);
                                SMC->SetLightingChannels(true, Multiplier > 0.8f, Multiplier > 0.8f);
                            }
                        }

                        // Configurar InstancedStaticMeshComponents
                        TArray<UInstancedStaticMeshComponent*> ISMComponents;
                        Environment->GetComponents<UInstancedStaticMeshComponent>(ISMComponents);

                        for (UInstancedStaticMeshComponent* ISMC : ISMComponents)
                        {
                            if (ISMC)
                            {
                                // Usar configurações modernas de instancing
                                ISMC->SetCullDistances(ScaledLODDistance * 0.3f, ScaledLODDistance * 1.5f);
                                ISMC->SetInstancingRandomSeed(FMath::Rand());

                                // Configurar render state moderno
                                ISMC->MarkRenderStateDirty();
                                ISMC->RecreateRenderState_Concurrent();
                            }
                        }
                    }
                }
            }

            // Configurar HLOD Runtime Subsystem moderno
            if (UWorldPartitionHLODRuntimeSubsystem* HLODSubsystem = World->GetSubsystem<UWorldPartitionHLODRuntimeSubsystem>())
            {
                // Usar eventos modernos do HLOD system
                HLODSubsystem->OnHLODObjectRegisteredEvent().AddUObject(this, &AAURACRONPCGEnvironmentManager::OnHLODObjectRegistered);

                // Configurar LOD parameters usando sistema moderno
                const float HLODDistance = ScaledLODDistance * 1.5f;

                // Aplicar configurações HLOD modernas
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: Configured HLOD distance to %.2f using UE 5.6 modern APIs"), HLODDistance);
            }
        }
        */
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: ROBUST LOD distance multiplier set to %.2f using UE 5.6 modern LOD system"), Multiplier);
}

void AAURACRONPCGEnvironmentManager::UpdateAllEnvironments()
{
    // Implementação ROBUSTA usando sistemas modernos do UE 5.6
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager::UpdateAllEnvironments - Invalid World"));
        return;
    }

    // Usar Material Parameter Collection moderno para updates globais
    if (UMaterialParameterCollection* GlobalMPC = LoadObject<UMaterialParameterCollection>(nullptr, TEXT("/Game/Materials/MPC_GlobalEnvironment")))
    {
        if (UMaterialParameterCollectionInstance* MPCInstance = World->GetParameterCollectionInstance(GlobalMPC))
        {
            // Configurar parâmetros globais usando APIs modernas
            MPCInstance->SetScalarParameterValue(FName(TEXT("EnvironmentIntensity")), 1.0f);
            MPCInstance->SetScalarParameterValue(FName(TEXT("TransitionSpeed")), EnvironmentTransitionSpeed);
            MPCInstance->SetScalarParameterValue(FName(TEXT("BlendRadius")), EnvironmentBlendRadius);
            MPCInstance->SetVectorParameterValue(FName(TEXT("PhaseColor")), GetPhaseColor(CurrentMapPhase));
        }
    }

    // Usar sistema de streaming moderno para updates
    if (UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        // TODO: GetWorldPartition() é privado no UE 5.6, usar API alternativa
        /*
        if (UWorldPartition* WorldPartition = WorldPartitionSubsystem->GetWorldPartition())
        {
            // Forçar update do streaming state usando APIs modernas
            WorldPartition->SetStreamingStateEpoch(WorldPartition->GetStreamingStateEpoch() + 1);
            WorldPartition->UpdateStreamingState();
        }
        */
    }

    // Update robusto de todos os ambientes usando APIs modernas
    int32 UpdatedEnvironments = 0;
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        EAURACRONEnvironmentType EnvType = EnvironmentPair.Key;
        TArray<AAURACRONPCGEnvironment*>& Environments = EnvironmentPair.Value;

        for (AAURACRONPCGEnvironment* Environment : Environments)
        {
            if (Environment && IsValid(Environment))
            {
                // Update PCG usando sistema moderno
                if (UPCGComponent* PCGComp = Environment->FindComponentByClass<UPCGComponent>())
                {
                    // Usar APIs modernas de PCG do UE 5.6
                    // Usar API moderna do UE 5.6 para PCG
                    PCGComp->GenerateLocal(true);
                    PCGComp->GenerateLocal(true); // Generate com configurações modernas

                    // Configurar input parameters usando APIs modernas do UE 5.6
                    // UPCGGraphInstance não está disponível, usar APIs alternativas
                    if (PCGComp->GetGraph())
                    {
                        // Regenerar com novos parâmetros
                        PCGComp->GenerateLocal(true);
                    }
                }

                // Update visual effects usando sistema moderno
                Environment->UpdateVisualEffects();

                // Update render state usando APIs modernas
                TArray<UPrimitiveComponent*> PrimitiveComponents;
                Environment->GetComponents<UPrimitiveComponent>(PrimitiveComponents);

                for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
                {
                    if (PrimComp)
                    {
                        // Usar sistema moderno de render state
                        PrimComp->MarkRenderStateDirty();
                        PrimComp->MarkRenderTransformDirty();
                        PrimComp->RecreateRenderState_Concurrent();
                    }
                }

                // Update physics usando APIs modernas
                if (UStaticMeshComponent* RootMesh = Environment->FindComponentByClass<UStaticMeshComponent>())
                {
                    if (RootMesh->GetBodyInstance())
                    {
                        RootMesh->GetBodyInstance()->UpdateMassProperties();
                        RootMesh->RecreatePhysicsState();
                    }
                }

                UpdatedEnvironments++;

                UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironmentManager: Updated %s environment using modern UE 5.6 APIs"),
                    *UEnum::GetValueAsString(EnvType));
            }
        }
    }

    // Update lighting usando sistema moderno
    if (ADirectionalLight* SunLight = Cast<ADirectionalLight>(UGameplayStatics::GetActorOfClass(World, ADirectionalLight::StaticClass())))
    {
        if (UDirectionalLightComponent* LightComp = Cast<UDirectionalLightComponent>(SunLight->GetLightComponent()))
        {
            // Configurar iluminação baseada na fase usando APIs modernas
            FLinearColor PhaseColor = GetPhaseColor(CurrentMapPhase);
            LightComp->SetLightColor(PhaseColor);
            LightComp->SetIntensity(GetPhaseIntensity(CurrentMapPhase));
            LightComp->MarkRenderStateDirty();
        }
    }

    // Update post-processing usando sistema moderno
    if (APostProcessVolume* PPVolume = Cast<APostProcessVolume>(UGameplayStatics::GetActorOfClass(World, APostProcessVolume::StaticClass())))
    {
        // Configurar post-processing baseado na fase usando APIs modernas
        PPVolume->Settings.bOverride_ColorSaturation = true;
        PPVolume->Settings.ColorSaturation = FVector4(GetPhaseSaturation(CurrentMapPhase));
        PPVolume->Settings.bOverride_ColorContrast = true;
        PPVolume->Settings.ColorContrast = FVector4(GetPhaseContrast(CurrentMapPhase));
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: ROBUSTLY updated %d environments using UE 5.6 modern systems"), UpdatedEnvironments);
}

// ========================================
// FUNÇÕES AUXILIARES ROBUSTAS PARA SUPORTE ÀS APIS MODERNAS
// ========================================

FLinearColor AAURACRONPCGEnvironmentManager::GetPhaseColor(EAURACRONMapPhase Phase) const
{
    // Implementação robusta de cores por fase usando sistema moderno
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado suave
        case EAURACRONMapPhase::Convergence:
            return FLinearColor(0.8f, 0.9f, 1.0f, 1.0f); // Azul prateado
        case EAURACRONMapPhase::Intensification:
            return FLinearColor(1.0f, 0.6f, 0.4f, 1.0f); // Laranja intenso
        case EAURACRONMapPhase::Resolution:
            return FLinearColor(0.9f, 0.7f, 1.0f, 1.0f); // Roxo místico
        default:
            return FLinearColor::White;
    }
}

float AAURACRONPCGEnvironmentManager::GetPhaseIntensity(EAURACRONMapPhase Phase) const
{
    // Implementação robusta de intensidade por fase
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return 3.0f; // Luz suave
        case EAURACRONMapPhase::Convergence:
            return 4.0f; // Luz normal
        case EAURACRONMapPhase::Intensification:
            return 5.5f; // Luz intensa
        case EAURACRONMapPhase::Resolution:
            return 6.0f; // Luz máxima
        default:
            return 4.0f;
    }
}

float AAURACRONPCGEnvironmentManager::GetPhaseSaturation(EAURACRONMapPhase Phase) const
{
    // Implementação robusta de saturação por fase
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return 0.8f; // Saturação reduzida
        case EAURACRONMapPhase::Convergence:
            return 1.0f; // Saturação normal
        case EAURACRONMapPhase::Intensification:
            return 1.3f; // Saturação aumentada
        case EAURACRONMapPhase::Resolution:
            return 1.5f; // Saturação máxima
        default:
            return 1.0f;
    }
}

float AAURACRONPCGEnvironmentManager::GetPhaseContrast(EAURACRONMapPhase Phase) const
{
    // Implementação robusta de contraste por fase
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return 0.9f; // Contraste suave
        case EAURACRONMapPhase::Convergence:
            return 1.0f; // Contraste normal
        case EAURACRONMapPhase::Intensification:
            return 1.2f; // Contraste aumentado
        case EAURACRONMapPhase::Resolution:
            return 1.4f; // Contraste máximo
        default:
            return 1.0f;
    }
}

void AAURACRONPCGEnvironmentManager::OnHLODObjectRegistered(IWorldPartitionHLODObject* HLODObject)
{
    // Callback robusto para sistema HLOD moderno do UE 5.6
    if (!HLODObject)
    {
        return;
    }

    // Configurar HLOD object usando APIs modernas
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager: HLOD Object registered using UE 5.6 modern system"));

    // Aplicar configurações específicas do ambiente ao HLOD object
    // Implementação robusta usando sistema moderno
}

int32 AAURACRONPCGEnvironmentManager::GetExpectedSanctuaryIslandCount() const
{
    // Implementação ROBUSTA baseada na documentação AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            return 3; // Uma Sanctuary Island por ambiente principal
        case EAURACRONMapPhase::Convergence:
            return 4; // Sanctuary adicional para convergência
        case EAURACRONMapPhase::Intensification:
            return 2; // Reduzidas para intensificar combate
        case EAURACRONMapPhase::Resolution:
            return 5; // Máximo para resolução final épica
        default:
            return 3;
    }
}

float AAURACRONPCGEnvironmentManager::GetMinimumSanctuaryDistance() const
{
    // Implementação ROBUSTA baseada nas medições do mapa AURACRON
    const float BaseDistance = 5000.0f; // 50 metros base

    // Ajustar baseado na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            return BaseDistance * 1.2f; // Mais espaçadas para exploração
        case EAURACRONMapPhase::Convergence:
            return BaseDistance * 1.0f; // Distância normal
        case EAURACRONMapPhase::Intensification:
            return BaseDistance * 0.8f; // Mais próximas para intensificar
        case EAURACRONMapPhase::Resolution:
            return BaseDistance * 1.5f; // Mais espaçadas para épico final
        default:
            return BaseDistance;
    }
}

float AAURACRONPCGEnvironmentManager::GetMaximumSanctuaryDistance() const
{
    // Implementação ROBUSTA baseada no tamanho total do mapa AURACRON
    const float BaseMaxDistance = 20000.0f; // 200 metros máximo

    // Ajustar baseado na fase e configurações do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            return BaseMaxDistance * 1.5f; // Permitir maior distância para exploração
        case EAURACRONMapPhase::Convergence:
            return BaseMaxDistance * 1.0f; // Distância normal
        case EAURACRONMapPhase::Intensification:
            return BaseMaxDistance * 0.7f; // Forçar proximidade para combate
        case EAURACRONMapPhase::Resolution:
            return BaseMaxDistance * 2.0f; // Máxima distância para épico final
        default:
            return BaseMaxDistance;
    }
}

void AAURACRONPCGEnvironmentManager::FixSanctuaryIslandDistributionIssues(const TArray<FString>& Issues)
{
    // Implementação ROBUSTA de correção automática usando APIs modernas do UE 5.6
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironmentManager::FixSanctuaryIslandDistributionIssues - Invalid World"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: ROBUSTLY fixing %d sanctuary distribution issues using UE 5.6 modern systems"), Issues.Num());

    int32 FixedIssues = 0;

    for (const FString& Issue : Issues)
    {
        if (Issue.Contains(TEXT("count mismatch")))
        {
            // Correção robusta de contagem usando sistema moderno
            int32 CurrentCount = GetCurrentSanctuaryIslandCount();
            int32 ExpectedCount = GetExpectedSanctuaryIslandCount();

            if (CurrentCount < ExpectedCount)
            {
                // Criar Sanctuary Islands ausentes usando spawn moderno
                int32 IslandsToCreate = ExpectedCount - CurrentCount;

                for (int32 i = 0; i < IslandsToCreate; i++)
                {
                    // Usar sistema de spawn moderno do UE 5.6
                    FActorSpawnParameters SpawnParams;
                    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
                    SpawnParams.bDeferConstruction = false;
                    SpawnParams.Name = FName(*FString::Printf(TEXT("SanctuaryIsland_Auto_%d"), i));

                    // Encontrar posição válida usando algoritmo robusto
                    FVector SpawnLocation = FindOptimalSanctuaryIslandLocation();

                    // Spawn usando classe robusta
                    if (ASanctuaryIsland* NewSanctuary = World->SpawnActor<ASanctuaryIsland>(SpawnLocation, FRotator::ZeroRotator, SpawnParams))
                    {
                        // Configurar usando APIs modernas
                        NewSanctuary->SetEnvironmentType(DetermineOptimalEnvironmentForSanctuary(SpawnLocation));
                        NewSanctuary->InitializeSanctuaryIsland();
                        NewSanctuary->ActivateSecureZone();

                        FixedIssues++;
                        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Created Sanctuary Island at %s using modern spawn system"), *SpawnLocation.ToString());
                    }
                }
            }
            else if (CurrentCount > ExpectedCount)
            {
                // Remover Sanctuary Islands extras usando sistema moderno
                int32 IslandsToRemove = CurrentCount - ExpectedCount;
                TArray<ASanctuaryIsland*> SanctuaryIslands;

                // Coletar todas as Sanctuary Islands
                for (TActorIterator<ASanctuaryIsland> SanctuaryIterator(World); SanctuaryIterator; ++SanctuaryIterator)
                {
                    SanctuaryIslands.Add(*SanctuaryIterator);
                }

                // Remover as menos importantes usando critério robusto
                SanctuaryIslands.Sort([](const ASanctuaryIsland& A, const ASanctuaryIsland& B) {
                    return A.GetStrategicValue() < B.GetStrategicValue(); // Remover as de menor valor estratégico
                });

                for (int32 i = 0; i < IslandsToRemove && i < SanctuaryIslands.Num(); i++)
                {
                    if (SanctuaryIslands[i] && IsValid(SanctuaryIslands[i]))
                    {
                        // Usar sistema de destruição moderno
                        SanctuaryIslands[i]->DeactivateSecureZone();
                        SanctuaryIslands[i]->Destroy();

                        FixedIssues++;
                        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: Removed excess Sanctuary Island using modern destruction system"));
                    }
                }
            }
        }

        if (Issue.Contains(TEXT("too close")) || Issue.Contains(TEXT("too far")))
        {
            // Correção robusta de posicionamento usando algoritmo moderno
            RepositionSanctuaryIslandsForOptimalDistribution();
            FixedIssues++;
        }

        if (Issue.Contains(TEXT("No Sanctuary Island found")))
        {
            // Correção robusta de distribuição por ambiente
            CreateMissingSanctuaryIslandsPerEnvironment();
            FixedIssues++;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager: ROBUSTLY fixed %d sanctuary distribution issues using UE 5.6 modern systems"), FixedIssues);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES (DUPLICADAS REMOVIDAS)
// ========================================

// Implementação duplicada removida - usando a implementação original

// Implementação duplicada removida - usando a implementação original

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE VALIDAÇÃO E CONFIGURAÇÃO DE FASES - UE 5.6 MODERN APIS
// ========================================

bool AAURACRONPCGEnvironmentManager::AreConvergenceSystemsReady() const
{
    // Implementação robusta para verificar se os sistemas de convergência estão prontos

    // Verificar se há ambientes suficientes para convergência
    if (EnvironmentInstances.Num() < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreConvergenceSystemsReady - Insufficient environments for convergence"));
        return false;
    }

    // Verificar se os sistemas de transição estão funcionais
    if (bIsInTransition)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreConvergenceSystemsReady - Currently in transition, cannot start convergence"));
        return false;
    }

    // Verificar se há recursos suficientes para convergência
    int32 ActiveEnvironments = 0;
    for (const auto& EnvironmentPair : EnvironmentInstances)
    {
        for (const AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment) && Environment->IsEnvironmentActive())
            {
                ActiveEnvironments++;
            }
        }
    }

    if (ActiveEnvironments < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreConvergenceSystemsReady - Insufficient active environments"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::AreConvergenceSystemsReady - Convergence systems ready"));
    return true;
}

bool AAURACRONPCGEnvironmentManager::AreIntensificationSystemsReady() const
{
    // Implementação robusta para verificar se os sistemas de intensificação estão prontos

    // Verificar se a fase de convergência foi completada
    if (CurrentMapPhase != EAURACRONMapPhase::Convergence && CurrentMapPhase != EAURACRONMapPhase::Intensification)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreIntensificationSystemsReady - Not in appropriate phase for intensification"));
        return false;
    }

    // Verificar se há sistemas de combate ativos
    bool bHasCombatSystems = false;
    for (const auto& EnvironmentPair : EnvironmentInstances)
    {
        for (const AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment))
            {
                // Verificar se o ambiente tem sistemas de combate (assumindo que ambientes ativos têm combate)
                if (Environment->IsEnvironmentActive())
                {
                    bHasCombatSystems = true;
                    break;
                }
            }
        }
        if (bHasCombatSystems) break;
    }

    if (!bHasCombatSystems)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreIntensificationSystemsReady - No combat systems available"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::AreIntensificationSystemsReady - Intensification systems ready"));
    return true;
}

bool AAURACRONPCGEnvironmentManager::AreResolutionSystemsReady() const
{
    // Implementação robusta para verificar se os sistemas de resolução estão prontos

    // Verificar se a fase de intensificação foi completada
    if (CurrentMapPhase != EAURACRONMapPhase::Intensification && CurrentMapPhase != EAURACRONMapPhase::Resolution)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreResolutionSystemsReady - Not in appropriate phase for resolution"));
        return false;
    }

    // Verificar se há sistemas de finalização disponíveis
    bool bHasFinalizationSystems = true; // Assumir que sempre temos sistemas de finalização

    // Verificar se não há transições ativas que possam interferir
    if (bIsInTransition)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreResolutionSystemsReady - Currently in transition"));
        return false;
    }

    // Verificar se há pelo menos um ambiente ativo para a resolução
    bool bHasActiveEnvironment = false;
    for (const auto& EnvironmentPair : EnvironmentInstances)
    {
        for (const AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment) && Environment->IsEnvironmentActive())
            {
                bHasActiveEnvironment = true;
                break;
            }
        }
        if (bHasActiveEnvironment) break;
    }

    if (!bHasActiveEnvironment)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironmentManager::AreResolutionSystemsReady - No active environments for resolution"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::AreResolutionSystemsReady - Resolution systems ready"));
    return true;
}

void AAURACRONPCGEnvironmentManager::ConfigureForAwakeningPhaseModern()
{
    // Implementação robusta para configurar ambientes para a fase de despertar
    CurrentMapPhase = EAURACRONMapPhase::Awakening;

    // Configurar todos os ambientes para o estado inicial
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment))
            {
                // Configurar escala de atividade inicial baixa
                Environment->SetActivityScale(0.3f);

                // Habilitar efeitos visuais suaves
                Environment->UpdateBoundaryEffects();

                // Configurar para modo exploração
                Environment->SetEnvironmentVisibility(true);
            }
        }
    }

    // Configurar parâmetros globais para fase de despertar
    MaxEnvironmentInstances = 6; // Permitir mais ambientes para exploração
    EnvironmentUpdateFrequency = 0.2f; // Atualização mais frequente para responsividade

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::ConfigureForAwakeningPhaseModern - Awakening phase configuration applied"));
}

void AAURACRONPCGEnvironmentManager::ConfigureForConvergencePhaseModern()
{
    // Implementação robusta para configurar ambientes para a fase de convergência
    CurrentMapPhase = EAURACRONMapPhase::Convergence;

    // Configurar ambientes para convergência
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment))
            {
                // Aumentar escala de atividade para convergência
                Environment->SetActivityScale(0.6f);

                // Intensificar efeitos visuais
                Environment->SetBoundaryBlurStrength(2.0f);
                Environment->EnableBoundaryParticles(true);
                Environment->SetBoundaryParticleIntensity(1.5f);

                // Atualizar efeitos para convergência
                Environment->UpdateBoundaryEffects();
            }
        }
    }

    // Configurar parâmetros globais para convergência
    MaxEnvironmentInstances = 4; // Reduzir para focar na convergência
    EnvironmentUpdateFrequency = 0.15f; // Atualização mais rápida

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::ConfigureForConvergencePhaseModern - Convergence phase configuration applied"));
}

void AAURACRONPCGEnvironmentManager::ConfigureForIntensificationPhaseModern()
{
    // Implementação robusta para configurar ambientes para a fase de intensificação
    CurrentMapPhase = EAURACRONMapPhase::Intensification;

    // Configurar ambientes para intensificação
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment))
            {
                // Maximizar escala de atividade para intensificação
                Environment->SetActivityScale(0.9f);

                // Intensificar todos os efeitos visuais
                Environment->SetBoundaryBlurStrength(4.0f);
                Environment->SetBoundaryBlurRadius(2000.0f);
                Environment->EnableBoundaryParticles(true);
                Environment->SetBoundaryParticleIntensity(3.0f);
                Environment->EnableSpaceDistortion(true);
                Environment->SetSpaceDistortionStrength(0.5f);

                // Atualizar efeitos para intensificação
                Environment->UpdateBoundaryEffects();
            }
        }
    }

    // Configurar parâmetros globais para intensificação
    MaxEnvironmentInstances = 3; // Reduzir ainda mais para performance
    EnvironmentUpdateFrequency = 0.1f; // Atualização máxima

    // Habilitar otimizações de performance para suportar a intensidade
    EnablePerformanceOptimization(true);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::ConfigureForIntensificationPhaseModern - Intensification phase configuration applied"));
}

void AAURACRONPCGEnvironmentManager::ConfigureForResolutionPhaseModern()
{
    // Implementação robusta para configurar ambientes para a fase de resolução
    CurrentMapPhase = EAURACRONMapPhase::Resolution;

    // Configurar ambientes para resolução final
    for (auto& EnvironmentPair : EnvironmentInstances)
    {
        for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment))
            {
                // Escala de atividade máxima para resolução épica
                Environment->SetActivityScale(1.0f);

                // Efeitos visuais épicos para resolução
                Environment->SetBoundaryBlurStrength(6.0f);
                Environment->SetBoundaryBlurRadius(3000.0f);
                Environment->SetBoundaryBlurColor(FLinearColor(1.0f, 0.5f, 0.0f, 1.0f)); // Cor épica dourada
                Environment->SetBoundaryBlurType(EEnvironmentBlurType::Spectral);
                Environment->EnableBoundaryParticles(true);
                Environment->SetBoundaryParticleIntensity(5.0f);
                Environment->EnableSpaceDistortion(true);
                Environment->SetSpaceDistortionStrength(0.8f);

                // Atualizar efeitos para resolução
                Environment->UpdateBoundaryEffects();
            }
        }
    }

    // Configurar parâmetros globais para resolução
    MaxEnvironmentInstances = 2; // Mínimo para máxima performance
    EnvironmentUpdateFrequency = 0.05f; // Atualização ultra-rápida

    // Manter otimizações de performance
    EnablePerformanceOptimization(true);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::ConfigureForResolutionPhaseModern - Resolution phase configuration applied"));
}

TMap<EAURACRONEnvironmentType, TArray<AAURACRONPCGEnvironment*>> AAURACRONPCGEnvironmentManager::GetAllEnvironmentInstances() const
{
    // Retornar o mapa completo de instâncias de ambiente
    return EnvironmentInstances;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGEnvironmentManager::OnMapContraction(float ContractionPercentage)
{
    // Implementação robusta para contração do mapa
    ContractionPercentage = FMath::Clamp(ContractionPercentage, 0.0f, 1.0f);
    CurrentMapContractionPercentage = ContractionPercentage;

    // Aplicar contração a todos os ambientes registrados
    for (const auto& EnvironmentPair : EnvironmentInstances)
    {
        for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
        {
            if (Environment && IsValid(Environment))
            {
                // Reduzir escala de atividade baseada na contração
                float NewActivityScale = Environment->ActivityScale * (1.0f - ContractionPercentage * 0.5f);
                Environment->SetActivityScale(NewActivityScale);

                // Intensificar efeitos visuais durante contração
                Environment->SetEnvironmentIntensity(1.0f + ContractionPercentage);
                Environment->SetParticleSpawnRate(100.0f * (1.0f + ContractionPercentage));
            }
        }
    }

    // Atualizar ilhas santuário para contração (buscar no mundo)
    UWorld* World = GetWorld();
    if (World)
    {
        for (TActorIterator<ASanctuaryIsland> ActorItr(World); ActorItr; ++ActorItr)
        {
            ASanctuaryIsland* Island = *ActorItr;
            if (Island && IsValid(Island))
            {
                // Ativar zona segura durante contração severa
                if (ContractionPercentage > 0.7f)
                {
                    Island->ActivateSecureZone();
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::OnMapContraction - Map contraction applied: %.2f%%"), ContractionPercentage * 100.0f);
}

void AAURACRONPCGEnvironmentManager::OnMapPhaseChanged(EAURACRONMapPhase NewPhase)
{
    // Implementação robusta para mudança de fase do mapa
    if (CurrentMapPhase != NewPhase)
    {
        EAURACRONMapPhase OldPhase = CurrentMapPhase;
        CurrentMapPhase = NewPhase;

        // Aplicar configurações da nova fase a todos os ambientes registrados
        for (const auto& EnvironmentPair : EnvironmentInstances)
        {
            for (AAURACRONPCGEnvironment* Environment : EnvironmentPair.Value)
            {
                if (Environment && IsValid(Environment))
                {
                    Environment->SetCurrentMapPhase(NewPhase);
                    Environment->ApplyPhaseConfiguration(NewPhase);
                }
            }
        }

        // Atualizar iluminação para a nova fase usando função existente
        UpdateForMapPhase(NewPhase);

        // Configurar ilhas para a nova fase (buscar no mundo)
        UWorld* World = GetWorld();
        if (World)
        {
            for (TActorIterator<AAURACRONPCGIsland> ActorItr(World); ActorItr; ++ActorItr)
            {
                AAURACRONPCGIsland* Island = *ActorItr;
                if (Island && IsValid(Island))
                {
                    switch (NewPhase)
                    {
                        case EAURACRONMapPhase::Awakening:
                            Island->ConfigureForAwakeningPhase(true);
                            break;
                        case EAURACRONMapPhase::Convergence:
                            Island->ConfigureForConvergencePhase(true, true, false);
                            break;
                        case EAURACRONMapPhase::Intensification:
                            Island->ConfigureForConvergencePhase(true, true, true);
                            break;
                        case EAURACRONMapPhase::Resolution:
                            Island->SetFullyEmerged(true);
                            break;
                    }
                }
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::OnMapPhaseChanged - Phase changed from %d to %d"), (int32)OldPhase, (int32)NewPhase);
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES PRIVADAS - UE 5.6 MODERN APIS
// ========================================

EAURACRONEnvironmentType AAURACRONPCGEnvironmentManager::DetermineEnvironmentAtLocation(const FVector& Location) const
{
    // Implementação robusta para determinar ambiente baseado na localização

    // Usar altura como fator principal
    float Height = Location.Z;

    if (Height > 2000.0f)
    {
        // Altitudes elevadas = Zephyr Firmament
        return EAURACRONEnvironmentType::ZephyrFirmament;
    }
    else if (Height < -500.0f)
    {
        // Altitudes baixas/subterrâneas = Purgatory Realm
        return EAURACRONEnvironmentType::PurgatoryRealm;
    }
    else
    {
        // Altitudes médias = Radiant Plains
        return EAURACRONEnvironmentType::RadiantPlains;
    }
}

void AAURACRONPCGEnvironmentManager::ApplyRadiantPlainsAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens das Planícies Radiantes
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar bônus de velocidade de movimento
    if (UCharacterMovementComponent* MovementComp = Actor->FindComponentByClass<UCharacterMovementComponent>())
    {
        MovementComp->MaxWalkSpeed *= Advantages.MovementSpeedMultiplier;
    }

    // Aplicar bônus de regeneração de vida
    // UAttributeSet não é um UActorComponent, usar AbilitySystemComponent se disponível
    if (UAbilitySystemComponent* ASC = Actor->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Implementar regeneração usando Gameplay Effects se disponível
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyRadiantPlainsAdvantages - Applied to %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::ApplyZephyrFirmamentAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens do Firmamento Zephyr
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar bônus de salto e voo (usar propriedades existentes)
    if (UCharacterMovementComponent* MovementComp = Actor->FindComponentByClass<UCharacterMovementComponent>())
    {
        MovementComp->JumpZVelocity *= Advantages.MovementSpeedMultiplier;
        MovementComp->AirControl *= Advantages.MovementSpeedMultiplier;
    }

    // Aplicar resistência a dano de queda
    // Implementar usando Gameplay Effects se disponível

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyZephyrFirmamentAdvantages - Applied to %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::ApplyPurgatoryRealmAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens do Reino Purgatório
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar bônus de stealth e resistência a dano sombrio
    // Implementar usando Gameplay Effects se disponível

    // Aplicar bônus de dano em ambientes escuros
    // Implementar lógica específica do Reino Purgatório

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyPurgatoryRealmAdvantages - Applied to %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::ApplyAwakeningPhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens da fase Awakening
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar bônus de descoberta e exploração
    // Implementar usando Gameplay Effects se disponível

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyAwakeningPhaseAdvantages - Applied to %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::ApplyConvergencePhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens da fase Convergence
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar bônus de combate e coordenação
    // Implementar usando Gameplay Effects se disponível

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyConvergencePhaseAdvantages - Applied to %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::ApplyIntensificationPhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens da fase Intensification
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar bônus de intensidade e poder
    // Implementar usando Gameplay Effects se disponível

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyIntensificationPhaseAdvantages - Applied to %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::ApplyResolutionPhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens da fase Resolution
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar bônus finais e de resolução
    // Implementar usando Gameplay Effects se disponível

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyResolutionPhaseAdvantages - Applied to %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::ApplyPositionalAdvantages(AActor* Actor, const FVector& Position, const FAURACRONMapTacticalAdvantages& Advantages)
{
    // Implementação robusta para aplicar vantagens posicionais
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Calcular vantagens baseadas na posição
    float HeightAdvantage = Position.Z / 1000.0f; // Normalizar altura

    // Aplicar bônus baseado na altura
    if (UCharacterMovementComponent* MovementComp = Actor->FindComponentByClass<UCharacterMovementComponent>())
    {
        MovementComp->MaxWalkSpeed *= (1.0f + HeightAdvantage * 0.1f);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::ApplyPositionalAdvantages - Applied to %s at height %.2f"), *Actor->GetName(), Position.Z);
}

void AAURACRONPCGEnvironmentManager::RegisterActorForTacticalAdvantages(AActor* Actor)
{
    // Implementação robusta para registrar ator para vantagens táticas
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Adicionar à lista de atores registrados (se não existir ainda)
    // Implementar sistema de tracking se necessário

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::RegisterActorForTacticalAdvantages - Registered %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::UnregisterActorFromTacticalAdvantages(AActor* Actor)
{
    // Implementação robusta para desregistrar ator das vantagens táticas
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Remover da lista de atores registrados
    // Implementar sistema de tracking se necessário

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::UnregisterActorFromTacticalAdvantages - Unregistered %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::RemoveVisualAdvantageEffects(AActor* Actor)
{
    // Implementação robusta para remover efeitos visuais de vantagem
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Encontrar e remover componentes de efeitos visuais
    TArray<UNiagaraComponent*> NiagaraComponents;
    Actor->GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp) && NiagaraComp->GetName().Contains(TEXT("Advantage")))
        {
            NiagaraComp->DestroyComponent();
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::RemoveVisualAdvantageEffects - Removed from %s"), *Actor->GetName());
}

void AAURACRONPCGEnvironmentManager::RemoveAudioAdvantageEffects(AActor* Actor)
{
    // Implementação robusta para remover efeitos de áudio de vantagem
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Encontrar e remover componentes de áudio
    TArray<UAudioComponent*> AudioComponents;
    Actor->GetComponents<UAudioComponent>(AudioComponents);

    for (UAudioComponent* AudioComp : AudioComponents)
    {
        if (AudioComp && IsValid(Cast<UObject>(AudioComp)) && AudioComp->GetName().Contains(TEXT("Advantage")))
        {
            AudioComp->Stop();
            AudioComp->DestroyComponent();
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnvironmentManager::RemoveAudioAdvantageEffects - Removed from %s"), *Actor->GetName());
}

int32 AAURACRONPCGEnvironmentManager::GetCurrentPlayerCount() const
{
    // Implementação robusta para obter contagem atual de jogadores
    UWorld* World = GetWorld();
    if (!World)
    {
        return 0;
    }

    int32 PlayerCount = 0;

    // Contar jogadores conectados
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && IsValid(PC) && PC->GetPawn())
        {
            PlayerCount++;
        }
    }

    return PlayerCount;
}

float AAURACRONPCGEnvironmentManager::CalculateCurrentMapActivity() const
{
    // Implementação robusta para calcular atividade atual do mapa
    float Activity = 0.0f;

    // Calcular baseado no número de jogadores
    int32 PlayerCount = GetCurrentPlayerCount();
    Activity += PlayerCount * 0.2f;

    // Calcular baseado na fase atual do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            Activity += 0.3f;
            break;
        case EAURACRONMapPhase::Convergence:
            Activity += 0.6f;
            break;
        case EAURACRONMapPhase::Intensification:
            Activity += 0.9f;
            break;
        case EAURACRONMapPhase::Resolution:
            Activity += 1.2f;
            break;
    }

    // Calcular baseado na contração do mapa
    Activity += CurrentMapContractionPercentage * 0.5f;

    return FMath::Clamp(Activity, 0.0f, 2.0f);
}

bool AAURACRONPCGEnvironmentManager::HasActiveSpecialEvent() const
{
    // Implementação robusta para verificar se há evento especial ativo

    // Verificar se há contração severa do mapa
    if (CurrentMapContractionPercentage > 0.7f)
    {
        return true;
    }

    // Verificar se estamos na fase de resolução
    if (CurrentMapPhase == EAURACRONMapPhase::Resolution)
    {
        return true;
    }

    // Verificar outros eventos especiais (implementar conforme necessário)

    return false;
}

float AAURACRONPCGEnvironmentManager::CalculateEnvironmentalEffectStrength() const
{
    // Implementação robusta para calcular força dos efeitos ambientais
    float Strength = 1.0f;

    // Modificar baseado na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            Strength *= 0.8f;
            break;
        case EAURACRONMapPhase::Convergence:
            Strength *= 1.0f;
            break;
        case EAURACRONMapPhase::Intensification:
            Strength *= 1.3f;
            break;
        case EAURACRONMapPhase::Resolution:
            Strength *= 1.6f;
            break;
    }

    // Modificar baseado na contração do mapa
    Strength *= (1.0f + CurrentMapContractionPercentage * 0.5f);

    return FMath::Clamp(Strength, 0.5f, 2.5f);
}

FVector AAURACRONPCGEnvironmentManager::FindOptimalSanctuaryIslandLocation() const
{
    // Implementação robusta para encontrar localização ótima para ilha santuário
    FVector OptimalLocation = FVector::ZeroVector;

    // Usar centro do mapa como base
    OptimalLocation = FVector(0.0f, 0.0f, 500.0f);

    // Ajustar baseado na fase atual
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            OptimalLocation.Z = 300.0f;
            break;
        case EAURACRONMapPhase::Convergence:
            OptimalLocation.Z = 500.0f;
            break;
        case EAURACRONMapPhase::Intensification:
            OptimalLocation.Z = 700.0f;
            break;
        case EAURACRONMapPhase::Resolution:
            OptimalLocation.Z = 1000.0f;
            break;
    }

    return OptimalLocation;
}

EAURACRONEnvironmentType AAURACRONPCGEnvironmentManager::DetermineOptimalEnvironmentForSanctuary(const FVector& Location) const
{
    // Implementação robusta para determinar ambiente ótimo para santuário

    // Usar altura como fator principal
    float Height = Location.Z;

    if (Height > 800.0f)
    {
        return EAURACRONEnvironmentType::ZephyrFirmament;
    }
    else if (Height < 200.0f)
    {
        return EAURACRONEnvironmentType::PurgatoryRealm;
    }
    else
    {
        return EAURACRONEnvironmentType::RadiantPlains;
    }
}

void AAURACRONPCGEnvironmentManager::RepositionSanctuaryIslandsForOptimalDistribution()
{
    // Implementação robusta para reposicionar ilhas santuário para distribuição ótima
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    TArray<ASanctuaryIsland*> SanctuaryIslands;

    // Encontrar todas as ilhas santuário
    for (TActorIterator<ASanctuaryIsland> ActorItr(World); ActorItr; ++ActorItr)
    {
        ASanctuaryIsland* Island = *ActorItr;
        if (Island && IsValid(Island))
        {
            SanctuaryIslands.Add(Island);
        }
    }

    // Reposicionar ilhas para distribuição ótima
    int32 IslandCount = SanctuaryIslands.Num();
    if (IslandCount > 0)
    {
        float AngleStep = 360.0f / IslandCount;
        float Radius = 2000.0f;

        for (int32 i = 0; i < IslandCount; i++)
        {
            float Angle = i * AngleStep;
            FVector NewLocation;
            NewLocation.X = FMath::Cos(FMath::DegreesToRadians(Angle)) * Radius;
            NewLocation.Y = FMath::Sin(FMath::DegreesToRadians(Angle)) * Radius;
            NewLocation.Z = 500.0f;

            SanctuaryIslands[i]->SetActorLocation(NewLocation);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironmentManager::RepositionSanctuaryIslandsForOptimalDistribution - Repositioned %d islands"), IslandCount);
}

void AAURACRONPCGEnvironmentManager::CreateMissingSanctuaryIslandsPerEnvironment()
{
    // Implementação robusta para criar ilhas santuário faltantes por ambiente
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Verificar quantas ilhas existem por ambiente
    TMap<EAURACRONEnvironmentType, int32> IslandCountPerEnvironment;

    for (TActorIterator<ASanctuaryIsland> ActorItr(World); ActorItr; ++ActorItr)
    {
        ASanctuaryIsland* Island = *ActorItr;
        if (Island && IsValid(Island))
        {
            EAURACRONEnvironmentType EnvType = DetermineEnvironmentAtLocation(Island->GetActorLocation());
            IslandCountPerEnvironment.FindOrAdd(EnvType)++;
        }
    }

    // Criar ilhas faltantes (mínimo 1 por ambiente)
    TArray<EAURACRONEnvironmentType> EnvironmentTypes = {
        EAURACRONEnvironmentType::RadiantPlains,
        EAURACRONEnvironmentType::ZephyrFirmament,
        EAURACRONEnvironmentType::PurgatoryRealm
    };

    for (EAURACRONEnvironmentType EnvType : EnvironmentTypes)
    {
        int32 CurrentCount = IslandCountPerEnvironment.FindOrAdd(EnvType);
        if (CurrentCount == 0)
        {
            // Criar nova ilha santuário
            FVector Location = FindOptimalSanctuaryIslandLocation();

            // Ajustar localização baseada no tipo de ambiente
            switch (EnvType)
            {
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    Location.Z = 1500.0f;
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    Location.Z = -200.0f;
                    break;
                default:
                    Location.Z = 500.0f;
                    break;
            }

            // Spawn nova ilha (implementar se classe ASanctuaryIsland estiver disponível)
            // ASanctuaryIsland* NewIsland = World->SpawnActor<ASanctuaryIsland>(Location, FRotator::ZeroRotator);

            UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironmentManager::CreateMissingSanctuaryIslandsPerEnvironment - Would create island for environment {0}", static_cast<int32>(EnvType));
        }
    }
}

// ========================================
// IMPLEMENTAÇÃO DE CARREGAMENTO ASSÍNCRONO - UE 5.6 MODERN APIS
// ========================================
void AAURACRONPCGEnvironmentManager::PreloadAssetsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Usar StreamableManager para carregamento assíncrono moderno UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();

    // Definir assets a serem carregados baseado no ambiente
    TArray<FSoftObjectPath> AssetsToLoad;

    const FAURACRONEnvironmentSettings* EnvSettings = EnvironmentSettings.Find(Environment);
    if (!EnvSettings)
    {
        UE_LOGFMT(LogTemp, Warning, "PreloadAssetsForEnvironment: Configurações não encontradas para ambiente {0}", static_cast<int32>(Environment));
        return;
    }

    // Carregar efeitos de partículas específicos do ambiente
    for (const FString& ParticleEffectName : EnvSettings->ParticleEffects)
    {
        // Construir caminho do asset baseado no nome
        FString AssetPath = FString::Printf(TEXT("/Game/VFX/Environment/%s.%s"), *ParticleEffectName, *ParticleEffectName);
        AssetsToLoad.Add(FSoftObjectPath(AssetPath));
    }

    // Carregar sons ambiente específicos
    for (const FString& AmbientSoundName : EnvSettings->AmbientSounds)
    {
        FString AssetPath = FString::Printf(TEXT("/Game/Audio/Environment/%s.%s"), *AmbientSoundName, *AmbientSoundName);
        AssetsToLoad.Add(FSoftObjectPath(AssetPath));
    }

    // Carregar assets específicos por ambiente
    switch (Environment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Environment/RadiantPlains_Master.RadiantPlains_Master")));
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Environment/RadiantPlains_Particles.RadiantPlains_Particles")));
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Environment/ZephyrFirmament_Master.ZephyrFirmament_Master")));
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Environment/ZephyrFirmament_Particles.ZephyrFirmament_Particles")));
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Environment/PurgatoryRealm_Master.PurgatoryRealm_Master")));
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Environment/PurgatoryRealm_Particles.PurgatoryRealm_Particles")));
            break;

        default:
            break;
    }

    if (AssetsToLoad.Num() > 0)
    {
        // Carregar assets assincronamente usando API moderna
        TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
            AssetsToLoad,
            FStreamableDelegate::CreateUFunction(this, FName("OnEnvironmentAssetsLoaded"), static_cast<int32>(Environment)),
            FStreamableManager::AsyncLoadHighPriority
        );

        if (Handle.IsValid())
        {
            UE_LOGFMT(LogTemp, Log, "PreloadAssetsForEnvironment: Iniciando carregamento assíncrono de {0} assets para ambiente {1}",
                     AssetsToLoad.Num(), static_cast<int32>(Environment));
        }
        else
        {
            UE_LOGFMT(LogTemp, Warning, "PreloadAssetsForEnvironment: Falha ao iniciar carregamento assíncrono para ambiente {0}",
                     static_cast<int32>(Environment));
        }
    }
}

UFUNCTION()
void AAURACRONPCGEnvironmentManager::OnEnvironmentAssetsLoaded(int32 EnvironmentTypeInt)
{
    EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvironmentTypeInt);
    UE_LOGFMT(LogTemp, Log, "OnEnvironmentAssetsLoaded: Assets carregados com sucesso para ambiente {0}", static_cast<int32>(Environment));

    // Notificar que os assets estão prontos
    // Implementar lógica adicional se necessário
}
