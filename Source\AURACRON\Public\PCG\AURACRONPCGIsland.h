// AURACRONPCGIsland.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Classe para gerenciar as ilhas flutuantes

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Components/SphereComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "NiagaraComponent.h"
#include "Engine/StreamableManager.h"
#include "Engine/TimerManager.h"
#include "AURACRONPCGIsland.generated.h"

class UPCGComponent;
class UNiagaraComponent;
class UAudioComponent;
class UPointLightComponent;

/**
 * Ator para gerenciar uma ilha flutuante específica no AURACRON
 * Cada tipo de ilha (Sanctuary, Nexus, Battlefield) terá sua própria instância
 */
UCLASS()
class AURACRON_API AAURACRONPCGIsland : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGIsland();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Configurar o tipo de ilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetIslandType(EAURACRONIslandType NewType);

    // Obter o tipo de ilha
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    EAURACRONIslandType GetIslandType() const { return IslandType; }

    // Gerar a ilha procedural
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateIsland();

    // Atualizar a ilha com base na fase do mapa
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);

    // Definir a visibilidade da ilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetIslandVisibility(bool bVisible);

    // ========================================
    // FUNÇÕES DE BOUNDARY EFFECTS - UE 5.6 MODERN APIS
    // ========================================

    /** Definir intensidade do blur de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void SetBoundaryBlurStrength(float BlurStrength);

    /** Obter intensidade atual do blur de boundary */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|BoundaryEffects")
    float GetBoundaryBlurStrength() const { return CurrentBoundaryBlurStrength; }

    /** Atualizar todos os efeitos de boundary */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|BoundaryEffects")
    void UpdateBoundaryEffects();

    // ========================================
    // FUNÇÕES DE MAP PHASE - UE 5.6 MODERN APIS
    // ========================================

    /** Obter fase atual do mapa */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|MapPhase")
    EAURACRONMapPhase GetCurrentMapPhase() const { return CurrentMapPhase; }

    /** Definir fase atual do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|MapPhase")
    void SetCurrentMapPhase(EAURACRONMapPhase NewPhase);

    /** Aplicar configuração para uma fase específica */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|MapPhase")
    void ApplyPhaseConfiguration(EAURACRONMapPhase Phase);

    // Definir a escala de atividade da ilha (0.0 = preview, 1.0 = totalmente ativo)
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetActivityScale(float Scale);
    
    // Configurar ilha para Fase 1: Despertar
    UFUNCTION(BlueprintCallable, Category = "Island")
    void ConfigureForAwakeningPhase(bool bIsEntryDevice);
    
    // Configurar ilha para Fase 2: Convergência
    UFUNCTION(BlueprintCallable, Category = "Island")
    void ConfigureForConvergencePhase(bool bIsEntryDevice, bool bIsMidDevice, bool bIsHighDevice);
    
    // Definir se a ilha está totalmente emergida
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetFullyEmerged(bool bFullyEmerged);

    // Definir a posição da ilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetIslandPosition(const FVector& Position);

    // Definir o tamanho da ilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetIslandSize(float Size);

    // Obter o tamanho da ilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    float GetIslandSize() const;

    // Definir a altura da ilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetIslandHeight(float Height);

    // Funções auxiliares para configuração da ilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetIslandScale(float NewScale);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetEmergenceLevel(float NewLevel);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetActivityLevel(float NewLevel);

    // ========================================
    // FUNÇÕES DOS SISTEMAS DA DOCUMENTAÇÃO - UE 5.6 MODERN APIS
    // ========================================

    // Inicializar sistemas de Trilhos dinâmicos
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Trilhos")
    void InitializeTrilhosSystems();

    // Atualizar sistemas de Trilhos dinâmicos
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Trilhos")
    void UpdateTrilhosSystems();

    // Inicializar Fluxo Prismal
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|FluxoPrismal")
    void InitializeFluxoPrismal();

    // Atualizar Fluxo Prismal
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|FluxoPrismal")
    void UpdateFluxoPrismal();

    // Inicializar Ilha Central Auracron
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|IlhaCentral")
    void InitializeIlhaCentralAuracron();

    // Função de timer otimizada
    UFUNCTION()
    void UpdateIslandTimer();

    // Carregamento assíncrono de materiais
    void LoadMaterialAsync(const FString& MaterialPath, FStreamableDelegate OnLoadedDelegate);

    // Callback para material carregado
    UFUNCTION()
    void OnRuneMaterialLoaded(UStaticMeshComponent* RuneComponent, int32 RuneIndex);

    // Atualizar efeitos visuais da fase
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Phase")
    void UpdatePhaseVisualEffects();

    // Atualizar áudio ambiente
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Audio")
    void UpdateAmbientAudio();

    // Getter para força do blur
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Boundary")
    float GetBoundaryBlurStrength() const;

public:
    // Componente PCG principal para geração da ilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    UPCGComponent* PCGComponent;

    // Componente de colisão da ilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    USphereComponent* CollisionComponent;

    // Mesh principal da ilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    UStaticMeshComponent* IslandMesh;

    // Componente de áudio para efeitos sonoros
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    UAudioComponent* AudioComponent;

    // Componente de luz para iluminação dinâmica
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    UPointLightComponent* PointLightComponent;

    // Configurações PCG para esta ilha
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG")
    UPCGSettings* IslandSettings;

    // Características específicas da ilha
    // Sanctuary
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Sanctuary", meta = (EditCondition = "IslandType == EAURACRONIslandType::Sanctuary"))
    bool bHasHealingFountain;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Sanctuary", meta = (EditCondition = "IslandType == EAURACRONIslandType::Sanctuary"))
    bool bHasProtectiveBarrier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Sanctuary", meta = (EditCondition = "IslandType == EAURACRONIslandType::Sanctuary"))
    bool bHasAncientTree;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Sanctuary", meta = (EditCondition = "IslandType == EAURACRONIslandType::Sanctuary"))
    bool bHasResourceNodes;

    // Nexus
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Nexus", meta = (EditCondition = "IslandType == EAURACRONIslandType::Nexus"))
    bool bHasCentralSpire;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Nexus", meta = (EditCondition = "IslandType == EAURACRONIslandType::Nexus"))
    bool bHasEnergyVortex;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Nexus", meta = (EditCondition = "IslandType == EAURACRONIslandType::Nexus"))
    bool bHasAncientRunes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Nexus", meta = (EditCondition = "IslandType == EAURACRONIslandType::Nexus"))
    bool bHasPortalGateways;

    // Battlefield
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Battlefield", meta = (EditCondition = "IslandType == EAURACRONIslandType::Battlefield"))
    bool bHasStrategicPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Battlefield", meta = (EditCondition = "IslandType == EAURACRONIslandType::Battlefield"))
    bool bHasDestructibleElements;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Battlefield", meta = (EditCondition = "IslandType == EAURACRONIslandType::Battlefield"))
    bool bHasElevationChanges;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Battlefield", meta = (EditCondition = "IslandType == EAURACRONIslandType::Battlefield"))
    bool bHasHazardZones;

private:
    // Tipo de ilha
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG")
    EAURACRONIslandType IslandType;

    // Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)
    UPROPERTY()
    float ActivityScale;

    // Fase atual do mapa
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    // Intensidade atual do blur de boundary
    UPROPERTY()
    float CurrentBoundaryBlurStrength;

    // ========================================
    // VARIÁVEIS DOS SISTEMAS DA DOCUMENTAÇÃO - UE 5.6 MODERN APIS
    // ========================================

    // StreamableManager para carregamento assíncrono
    FStreamableManager* StreamableManager;

    // Timer handle para atualizações otimizadas
    FTimerHandle UpdateTimerHandle;

    // Sistemas de Trilhos dinâmicos
    UPROPERTY()
    bool bHasSolarTrilho;

    UPROPERTY()
    bool bHasAxisTrilho;

    UPROPERTY()
    bool bHasLunarTrilho;

    UPROPERTY()
    float SolarTrilhoIntensity;

    UPROPERTY()
    float AxisTrilhoIntensity;

    UPROPERTY()
    float LunarTrilhoIntensity;

    // Componentes dos Trilhos
    UPROPERTY()
    UNiagaraComponent* USolarTrilhoComponent;

    UPROPERTY()
    UNiagaraComponent* UAxisTrilhoComponent;

    UPROPERTY()
    UNiagaraComponent* ULunarTrilhoComponent;

    // Sistema de Fluxo Prismal
    UPROPERTY()
    bool bHasFluxoPrismal;

    UPROPERTY()
    float FluxoPrismalIntensity;

    UPROPERTY()
    UNiagaraComponent* UFluxoPrismalComponent;

    UPROPERTY()
    UAudioComponent* UFluxoPrismalAudioComponent;

    // Sistema de Ilha Central Auracron
    UPROPERTY()
    bool bHasIlhaCentralAuracron;

    // Componentes dos setores da Ilha Central
    UPROPERTY()
    UStaticMeshComponent* USetorNexusComponent;

    UPROPERTY()
    UStaticMeshComponent* USetorSanctuaryComponent;

    UPROPERTY()
    UStaticMeshComponent* USetorArsenalComponent;

    UPROPERTY()
    UStaticMeshComponent* USetorChaosComponent;

    UPROPERTY()
    UStaticMeshComponent* UTorreControleCentralComponent;

    // Indica se a ilha está totalmente emergida
    UPROPERTY()
    bool bIsFullyEmerged;

    // Tempo acumulado para animações
    UPROPERTY()
    float AccumulatedTime;

    // Componentes gerados dinamicamente
    UPROPERTY()
    TArray<UActorComponent*> GeneratedComponents;

    // Tamanho da ilha em unidades do Unreal (1 unidade = 1cm)
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG")
    float IslandSize;

    // Altura da ilha em unidades do Unreal (1 unidade = 1cm)
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG")
    float IslandHeight;

    // Tempo decorrido desde a última atualização da ilha
    UPROPERTY()
    float TimeSinceLastUpdate;

    // Intervalo de atualização da ilha em segundos
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG")
    float UpdateInterval;

    // Funções internas para geração de características específicas (legado)
    void GenerateSanctuary();
    void GenerateNexus();
    void GenerateBattlefield();

    // Funções modernas para geração de ilhas
    void ClearGeneratedElements();
    void ConfigureIslandDimensions();
    void PositionIsland();
    void GenerateNexusIsland();
    void GenerateSanctuaryIsland();
    void GenerateArsenalIsland();
    void GenerateChaosIsland();
    void ApplyMapPhaseEffects();
    void ApplyActivityScale();

    // Funções de utilidade para cálculos de ilha
    void CalculateIslandParameters();
    void UpdateIslandParameters();

public:
    /** Obter componente PCG para acesso externo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    UPCGComponent* GetPCGComponent() const { return PCGComponent; }

    /** Replicação de propriedades */
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    /** Callback quando ilha é ativada */
    void OnIslandActivated();

    /** Callback quando ilha é desativada */
    void OnIslandDeactivated();

    /** Atualizar ilha para nova fase */
    void UpdateIslandPhase(EAURACRONMapPhase NewPhase);

    /** Aplicar contração do mapa à ilha */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Island")
    void OnMapContraction(float ContractionFactor);
};