// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAURACRON_init() {}
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGPhaseManager_OnPhaseChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnEnvironmentChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnMovementStateChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnNearTrail__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature();
	AURACRON_API UFunction* Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AURACRON;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AURACRON()
	{
		if (!Z_Registration_Info_UPackage__Script_AURACRON.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AAURACRONPCGPhaseManager_OnPhaseChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnAxisTransitionAvailable__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnLunarVisionEffect__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AAURACRONPCGTrail_OnSolarHealthRegeneration__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnBandwidthLimitReached__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandsListChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnChaosManagerFullyInitialized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnDamageZoneDeactivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnEnvironmentChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnExclusiveAbilityCooldownChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnMapContraction__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnMapPhaseUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnMovementStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnNearTrail__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnNetworkOptimizationChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnNetworkStatsUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnPhaseTransitionProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnPlayerDamagedByZone__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnPlayerEnterTrailSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnPlayerInWarningZone__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilConsumed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilEquipped__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilFusion__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCancelled__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionNotification__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilFusionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilReplicationFusionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilSlotUnlocked__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilStatsChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilSystemStatsUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnSigilUnequipped__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnVFXCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnVFXStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AURACRON_OnVFXStatsChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnCombatAttributeChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDamageReceived__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnDeath__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealingReceived__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnHealthChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnManaChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnMovementSpeedChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnShieldReceived__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAURACRONAttributeSet_OnSigilAttributeChanged__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AURACRON",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x4D58C200,
				0x3CD1258D,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AURACRON.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AURACRON.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AURACRON(Z_Construct_UPackage__Script_AURACRON, TEXT("/Script/AURACRON"), Z_Registration_Info_UPackage__Script_AURACRON, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x4D58C200, 0x3CD1258D));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
