// AURACRONPCGEnvironmentManager.h
// Sistema de Gerenciamento dos 3 Ambientes Dinâmicos do AURACRON - UE 5.6
// <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Zephyr, <PERSON>ino Purgatório com rotação automática

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGLaneSystem.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "PCG/AURACRONPCGPortal.h"
#include "Components/StaticMeshComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/PostProcessComponent.h"
#include "Engine/DirectionalLight.h"
#include "Engine/SkyLight.h"
#include "Data/AURACRONEnums.h"
#include "Data/AURACRONStructs.h"
#include "AURACRONPCGEnvironmentManager.generated.h"

// Forward declarations
class AAURACRONPCGEnvironment;
class AAURACRONPCGPortal;
class IWorldPartitionHLODObject;

/**
 * Estrutura wrapper para array de ambientes (resolve problema UHT com TMap<Enum, TArray<Type>>)
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONEnvironmentArray
{
    GENERATED_BODY()

    UPROPERTY()
    TArray<AAURACRONPCGEnvironment*> Environments;

    FAURACRONEnvironmentArray()
    {
    }
};

/**
 * Estrutura para armazenar destinos de teletransporte para portais táticos
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONTeleportDestinations
{
    GENERATED_BODY()

    /** Localizações de destino para teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FVector> Locations;
    
    /** Rotações de destino para teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FRotator> Rotations;

    FAURACRONTeleportDestinations()
    {
    }
};

/**
 * Configurações de ambiente para diferentes tipos de dispositivos
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONDeviceEnvironmentSettings
{
    GENERATED_BODY()
    
    /** Se é um dispositivo de entrada (baixo desempenho) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsEntryDevice;
    
    /** Se todos os ambientes estão acessíveis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bAllEnvironmentsAccessible;
    
    /** Se os ambientes não ativos devem ser mostrados como "preview zones" */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowPreviewZones;
    
    FAURACRONDeviceEnvironmentSettings()
        : bIsEntryDevice(false)
        , bAllEnvironmentsAccessible(true)
        , bShowPreviewZones(false)
    {
    }
};

/**
 * Informações específicas de um ambiente
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONEnvironmentSettings
{
    GENERATED_BODY()

    /** Tipo do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONEnvironmentType EnvironmentType;
    
    /** Nome do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString EnvironmentName;
    
    /** Descrição das características */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Description;
    
    /** Altura base do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float BaseHeight;
    
    /** Cor da iluminação ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor AmbientLightColor;
    
    /** Intensidade da iluminação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float LightIntensity;
    
    /** Cor do fog/neblina */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor FogColor;
    
    /** Densidade do fog */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FogDensity;
    
    /** Configurações de post-processing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<FString, float> PostProcessSettings;
    
    /** Efeitos de partículas específicos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> ParticleEffects;
    
    /** Sons ambiente específicos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> AmbientSounds;
    
    /** Mecânicas únicas do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<FString, float> UniqueMechanics;
    
    /** Duração do ambiente em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float EnvironmentDuration;
    
    /** Se o ambiente está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive;
    
    FAURACRONEnvironmentSettings()
        : EnvironmentType(EAURACRONEnvironmentType::RadiantPlains)
        , EnvironmentName(TEXT("Planície Radiante"))
        , Description(TEXT("Planícies terrestres com vegetação exuberante"))
        , BaseHeight(0.0f)
        , AmbientLightColor(FLinearColor::White)
        , LightIntensity(1.0f)
        , FogColor(FLinearColor::White)
        , FogDensity(0.1f)
        , EnvironmentDuration(480.0f)
        , bIsActive(false)
    {
    }
};

/**
 * Gerenciador global dos 3 ambientes dinâmicos do AURACRON
 *
 * RESPONSABILIDADES CLARIFICADAS:
 * ================================
 *
 * ESTA CLASSE (AURACRONPCGEnvironmentManager) - GERENCIADOR GLOBAL:
 * - Orquestra a rotação automática entre os 3 ambientes principais
 * - Gerencia transições suaves e timing entre ambientes
 * - Coordena múltiplas instâncias de AURACRONPCGEnvironment
 * - Aplica efeitos globais (iluminação, pós-processamento, fog, skybox)
 * - Integra com outros sistemas (PhaseManager, ObjectiveSystem, LaneSystem)
 * - Controla configurações de ambiente por fase do mapa
 * - Gerencia efeitos temporários globais
 * - Notifica todos os sistemas sobre mudanças de ambiente
 * - Controla replicação de estado para multiplayer
 *
 * AURACRONPCGEnvironment - ATOR INDIVIDUAL:
 * - Representa uma instância específica de ambiente local
 * - Executa geração PCG de elementos específicos
 * - Responde a comandos do Manager
 * - Gerencia características locais do ambiente
 *
 * RELAÇÃO: Este Manager (1) -> Environment Instances (N)
 * O Manager é o "maestro", os Environments são os "músicos".
 */
UCLASS()
class AURACRON_API AAURACRONPCGEnvironmentManager : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGEnvironmentManager();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // ========================================
    // FUNÇÕES PÚBLICAS
    // ========================================
    
    /** Inicializar o sistema de ambientes */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void InitializeEnvironmentSystem();
    
    /** Configurar ambientes para dispositivo específico (Entry, Mid, High) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureForDeviceType(bool bIsEntryDevice);
    
    /** Configurar para Fase 1: Despertar */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureForAwakeningPhase(bool bIsEntryDevice);
    
    /** Configurar para Fase 2: Convergência */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureForConvergencePhase(bool bIsEntryDevice, bool bIsMidDevice, bool bIsHighDevice);
    
    /** Configurar transição suave para Firmamento Zephyr (dispositivos Entry) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureSmoothTransitionToZephyr(bool bShowAbyssPreview);
    
    /** Configurar 2 ambientes simultâneos com transições simplificadas (dispositivos Mid) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureSimultaneousEnvironments(bool bSimplifiedTransitions);
    
    /** Configurar fronteiras confusas entre ambientes (dispositivos High) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void ConfigureBlurredEnvironmentBoundaries(float BlurIntensity);
    
    /** Validar integração com o PCGPhaseManager */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    bool ValidatePhaseManagerIntegration();
    
    /** Corrigir problemas de integração com o PCGPhaseManager */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Environment")
    void FixPhaseManagerIntegrationIssues();
    
    /** Verifica e garante que as Ilhas Santuário sejam distribuídas em seções calmas do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ValidateSanctuaryIslandDistribution();
    
    /** Retorna o número atual de Ilhas Santuário no ambiente */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    int32 GetCurrentSanctuaryIslandCount() const;
    
    /** Iniciar rotação automática dos ambientes */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void StartEnvironmentRotation();
    
    /** Parar rotação automática */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void StopEnvironmentRotation();
    
    /** Forçar transição para ambiente específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ForceTransitionToEnvironment(EAURACRONEnvironmentType TargetEnvironment, float TransitionDuration = 30.0f);
    
    /** Aplicar vantagens táticas do mapa atual aos jogadores */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ApplyMapTacticalAdvantages(AActor* TargetActor);
    
    /** Remover vantagens táticas do mapa anterior */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void RemoveMapTacticalAdvantages(AActor* TargetActor);
    
    /** Obter vantagens táticas do mapa atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    FAURACRONMapTacticalAdvantages GetCurrentMapTacticalAdvantages() const;
    
    /** Obter ambiente atualmente ativo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    EAURACRONEnvironmentType GetCurrentEnvironment() const { return CurrentEnvironment; }
    
    /** Obter próximo ambiente na rotação */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    EAURACRONEnvironmentType GetNextEnvironment() const;
    
    /** Obter tempo restante no ambiente atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    float GetTimeRemainingInCurrentEnvironment() const;
    
    /** Obter progresso da transição atual (0.0 - 1.0) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    float GetTransitionProgress() const;
    
    /** Verificar se está em transição */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    bool IsInTransition() const { return bIsInTransition; }
    
    /** Obter configurações de um ambiente */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    FAURACRONEnvironmentSettings GetEnvironmentSettings(EAURACRONEnvironmentType Environment) const;
    
    /** Atualizar para nova fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    /** Aplicar efeitos especiais temporários */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ApplyTemporaryEnvironmentEffect(const FString& EffectName, float Duration);

    /** Aplicar contração do mapa a todos os ambientes */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Environment")
    void OnMapContraction(float ContractionFactor);

    // ========================================
    // FUNÇÕES DE VALIDAÇÃO E CONFIGURAÇÃO DE FASES - UE 5.6 MODERN APIS
    // ========================================

    /** Verificar se os sistemas de convergência estão prontos */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|PhaseValidation")
    bool AreConvergenceSystemsReady() const;

    /** Verificar se os sistemas de intensificação estão prontos */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|PhaseValidation")
    bool AreIntensificationSystemsReady() const;

    /** Verificar se os sistemas de resolução estão prontos */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|PhaseValidation")
    bool AreResolutionSystemsReady() const;

    /** Configurar ambientes para fase de despertar (versão moderna) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PhaseConfiguration")
    void ConfigureForAwakeningPhaseModern();

    /** Configurar ambientes para fase de convergência (versão moderna) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PhaseConfiguration")
    void ConfigureForConvergencePhaseModern();

    /** Configurar ambientes para fase de intensificação (versão moderna) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PhaseConfiguration")
    void ConfigureForIntensificationPhaseModern();

    /** Configurar ambientes para fase de resolução (versão moderna) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PhaseConfiguration")
    void ConfigureForResolutionPhaseModern();

    /** Callback para mudanças de fase do mapa */
    UFUNCTION()
    void OnMapPhaseChanged(EAURACRONMapPhase NewPhase);

    // ========================================
    // FUNÇÕES DE INTEGRAÇÃO COM ENVIRONMENT INSTANCES
    // ========================================

    /** Registrar uma instância de ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void RegisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance);

    /** Desregistrar uma instância de ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void UnregisterEnvironmentInstance(AAURACRONPCGEnvironment* EnvironmentInstance);

    /** Obter todas as instâncias de um tipo de ambiente */
    UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
    TArray<AAURACRONPCGEnvironment*> GetEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType) const;

    /** Obter todas as instâncias de ambiente (para validação) - C++ only */
    TMap<EAURACRONEnvironmentType, TArray<AAURACRONPCGEnvironment*>> GetAllEnvironmentInstances() const;

    /** Ativar todas as instâncias de um ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ActivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType);

    /** Desativar todas as instâncias de um ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void DeactivateEnvironmentInstances(EAURACRONEnvironmentType EnvironmentType);

    /** Aplicar transição a todas as instâncias */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
    void ApplyTransitionToInstances(EAURACRONEnvironmentType EnvironmentType, float TransitionProgress, bool bFadingIn);

protected:
    // ========================================
    // CONFIGURAÇÕES
    // ========================================
    
    /** Configurações dos 3 ambientes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    TMap<EAURACRONEnvironmentType, FAURACRONEnvironmentSettings> EnvironmentSettings;
    
    /** Número total de Ilhas Santuário a serem geradas (conforme documentação: 8) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager", meta = (ClampMin = "8", ClampMax = "8", UIMin = "8", UIMax = "8"))
    int32 TotalSanctuaryIslands = 8;
    
    /** Configurações de vantagens táticas para cada tipo de mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    TMap<EAURACRONEnvironmentType, FAURACRONMapTacticalAdvantages> MapTacticalAdvantages;
    
    /** Atores afetados pelas vantagens táticas do mapa atual */
    UPROPERTY()
    TArray<AActor*> ActorsWithTacticalAdvantages;
    
    /** Referências aos sistemas integrados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    AAURACRONPCGLaneSystem* LaneSystem;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    AAURACRONPCGJungleSystem* JungleSystem;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    AAURACRONPCGObjectiveSystem* ObjectiveSystem;
    
    /** Componentes de iluminação */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    UDirectionalLightComponent* DirectionalLight;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    USkyLightComponent* SkyLight;
    
    /** Componente de post-processing */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    UPostProcessComponent* PostProcessComponent;
    
    /** Se deve iniciar rotação automaticamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|EnvironmentManager")
    bool bAutoStartRotation;
    
    /** Se a rotação está ativa */
    UPROPERTY(BlueprintReadOnly, Category = "AURACRON|EnvironmentManager")
    bool bRotationActive;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================
    
    /** Ambiente atualmente ativo */
    UPROPERTY()
    EAURACRONEnvironmentType CurrentEnvironment;
    
    /** Ambiente de destino durante transição */
    UPROPERTY()
    EAURACRONEnvironmentType TargetEnvironment;
    
    /** Se está em transição */
    UPROPERTY()
    bool bIsInTransition;
    
    /** Tempo restante no ambiente atual */
    UPROPERTY()
    float TimeRemainingInEnvironment;
    
    /** Duração da transição atual */
    UPROPERTY()
    float CurrentTransitionDuration;
    
    /** Progresso da transição atual (0.0 - 1.0) */
    UPROPERTY()
    float TransitionProgress;
    
    /** Timer para rotação de ambientes */
    UPROPERTY()
    FTimerHandle EnvironmentRotationTimer;
    
    /** Timer para transições */
    UPROPERTY()
    FTimerHandle TransitionTimer;
    
    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    /** Percentual atual de contração do mapa */
    UPROPERTY()
    float CurrentMapContractionPercentage;
    
    /** Efeitos temporários ativos */
    UPROPERTY()
    TMap<FString, float> ActiveTemporaryEffects;

    /** Instâncias de ambiente registradas por tipo */
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, FAURACRONEnvironmentArray> RegisteredEnvironmentInstances;

    /** Mapa de instâncias para compatibilidade com código existente */
    TMap<EAURACRONEnvironmentType, TArray<AAURACRONPCGEnvironment*>> EnvironmentInstances;

    /** Portais táticos por ambiente (não serializado devido a limitações do TWeakObjectPtr em TArray) */
    TMap<EAURACRONEnvironmentType, TArray<TWeakObjectPtr<AAURACRONPCGPortal>>> TacticalPortals;
    
    /** Distribui as Ilhas Santuário em seções calmas do fluxo */
    void DistributeSanctuaryIslands();
    
    /** Identifica seções calmas do fluxo para posicionar Ilhas Santuário */
    TArray<FPrismalFlowSegment*> FindCalmFlowSections();
    
    /** Destinos de teletransporte por ambiente */
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, FAURACRONTeleportDestinations> TeleportDestinations;

    // ========================================
// FUNÇÕES DE GERENCIAMENTO DE PORTAIS TÁTICOS
// ========================================

/** Criar portais de posicionamento tático */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void CreateTacticalPortals();

/** Atualizar portais de posicionamento */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void UpdateTacticalPortals();

/** Ativar portais para um ambiente específico */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void ActivatePortalsForEnvironment(EAURACRONEnvironmentType Environment);

/** Desativar todos os portais */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void DeactivateAllPortals();

/** Obter portais para um ambiente específico */
UFUNCTION(BlueprintPure, Category = "AURACRON|EnvironmentManager")
TArray<class AAURACRONPCGPortal*> GetPortalsForEnvironment(EAURACRONEnvironmentType Environment) const;

/** Definir pontos de teletransporte para um ambiente */
UFUNCTION(BlueprintCallable, Category = "AURACRON|EnvironmentManager")
void SetTeleportDestinations(EAURACRONEnvironmentType Environment, const TArray<FVector>& Locations, const TArray<FRotator>& Rotations);

// ========================================
// FUNÇÕES INTERNAS
// ========================================

/** Inicializar configurações dos ambientes */
void InitializeEnvironmentSettings();

/** Configurar ambiente específico */
void SetupEnvironment(EAURACRONEnvironmentType Environment);

/** Iniciar transição para próximo ambiente */
void StartTransitionToNextEnvironment();

/** Executar transição suave */
void ExecuteTransition();

/** Finalizar transição */
void CompleteTransition();

/** Verificar se portal deve ser reposicionado para fase específica */
bool ShouldRepositionPortalForPhase(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase);

/** Calcular posição ótima para portal baseado na fase */
FVector CalculateOptimalPortalPosition(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase);

/** Aplicar configurações visuais do ambiente */
void ApplyEnvironmentVisuals(EAURACRONEnvironmentType Environment, float BlendWeight = 1.0f);

/** Aplicar mecânicas únicas do ambiente */
void ApplyEnvironmentMechanics(EAURACRONEnvironmentType Environment);

/** Notificar sistemas integrados sobre mudança de ambiente */
void NotifySystemsOfEnvironmentChange(EAURACRONEnvironmentType NewEnvironment);

/** Obter próximo ambiente na sequência */
EAURACRONEnvironmentType GetNextEnvironmentInSequence(EAURACRONEnvironmentType Current) const;

/** Aplicar efeitos da fase do mapa nos ambientes */
void ApplyMapPhaseEffects();

/** Atualizar efeitos temporários */
void UpdateTemporaryEffects(float DeltaTime);

/** Interpolar entre configurações de ambientes */
FAURACRONEnvironmentSettings InterpolateEnvironmentSettings(
    const FAURACRONEnvironmentSettings& From, 
    const FAURACRONEnvironmentSettings& To, 
    float Alpha
) const;

/** Criar portal de transição entre ambientes */
class AAURACRONPCGPortal* CreatePortal(EAURACRONEnvironmentType SourceEnvironment, EAURACRONEnvironmentType TargetEnvironment, const FVector& Location);

/** Criar portal tático em uma localização */
class AAURACRONPCGPortal* CreatePortalAtLocation(EAURACRONEnvironmentType Environment, const FVector& Location);

/** Atualizar destinos dos portais */
void UpdatePortalDestinations(EAURACRONEnvironmentType Environment);

/** Pré-carregar assets para próximo ambiente */
void PreloadAssetsForEnvironment(EAURACRONEnvironmentType Environment);

/** Callback para quando assets do ambiente são carregados */
UFUNCTION()
void OnEnvironmentAssetsLoaded(int32 EnvironmentTypeInt);

/** Função de atualização otimizada que substitui Tick */
void UpdateEnvironmentSystem();

/** Verificar se portal deve ser reposicionado para a fase atual */
bool ShouldRepositionPortalForPhase(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase) const;

/** Calcular posição ótima do portal baseada na fase do mapa */
FVector CalculateOptimalPortalPosition(AAURACRONPCGPortal* Portal, EAURACRONMapPhase MapPhase) const;

// ========================================
// PROPRIEDADES PRIVADAS ADICIONAIS
// ========================================

/** Número máximo de instâncias de ambiente ativas */
UPROPERTY()
int32 MaxEnvironmentInstances;

/** Frequência de atualização dos ambientes */
UPROPERTY()
float EnvironmentUpdateFrequency;

/** Se iluminação avançada está habilitada */
UPROPERTY()
bool bEnableAdvancedLighting;

/** Se fog volumétrico está habilitado */
UPROPERTY()
bool bEnableVolumetricFog;

/** Se partículas complexas estão habilitadas */
UPROPERTY()
bool bEnableComplexParticles;

/** Velocidade de transição entre ambientes */
UPROPERTY()
float EnvironmentTransitionSpeed;

/** Raio de blend entre ambientes */
UPROPERTY()
float EnvironmentBlendRadius;

/** Força do blur das bordas dos ambientes */
UPROPERTY()
float EnvironmentBoundaryBlurStrength;

/** Se ambientes simultâneos estão permitidos */
UPROPERTY()
bool bAllowSimultaneousEnvironments;

/** Número máximo de ambientes ativos simultaneamente */
UPROPERTY()
int32 MaxActiveEnvironments;

// Efeitos de gameplay para vantagens táticas
UPROPERTY()
TSubclassOf<class UGameplayEffect> RadiantPlainsAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> ZephyrFirmamentAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> PurgatoryRealmAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> AwakeningPhaseAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> ConvergencePhaseAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> IntensificationPhaseAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> ResolutionPhaseAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> HighGroundAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> CoverAdvantageEffect;

UPROPERTY()
TSubclassOf<class UGameplayEffect> FlankingAdvantageEffect;

// Funções auxiliares para implementação robusta
void EnablePerformanceOptimization(bool bEnable);
void SetLODDistanceMultiplier(float Multiplier);
void UpdateAllEnvironments();
int32 GetExpectedSanctuaryIslandCount() const;
float GetMinimumSanctuaryDistance() const;
float GetMaximumSanctuaryDistance() const;
void FixSanctuaryIslandDistributionIssues(const TArray<FString>& Issues);
EAURACRONEnvironmentType DetermineEnvironmentAtLocation(const FVector& Location) const;
void ApplyRadiantPlainsAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages);
void ApplyZephyrFirmamentAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages);
void ApplyPurgatoryRealmAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages);
void ApplyAwakeningPhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages);
void ApplyConvergencePhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages);
void ApplyIntensificationPhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages);
void ApplyResolutionPhaseAdvantages(AActor* Actor, const FAURACRONMapTacticalAdvantages& Advantages);
void ApplyPositionalAdvantages(AActor* Actor, const FVector& Location, const FAURACRONMapTacticalAdvantages& Advantages);
void RegisterActorForTacticalAdvantages(AActor* Actor);
void UnregisterActorFromTacticalAdvantages(AActor* Actor);
void RemoveVisualAdvantageEffects(AActor* Actor);
void RemoveAudioAdvantageEffects(AActor* Actor);
int32 GetCurrentPlayerCount() const;
float CalculateCurrentMapActivity() const;
bool HasActiveSpecialEvent() const;
float CalculateEnvironmentalEffectStrength() const;

// OnMapPhaseChanged movido para seção public

// Funções auxiliares para APIs modernas UE 5.6
FLinearColor GetPhaseColor(EAURACRONMapPhase Phase) const;
float GetPhaseIntensity(EAURACRONMapPhase Phase) const;
float GetPhaseSaturation(EAURACRONMapPhase Phase) const;
float GetPhaseContrast(EAURACRONMapPhase Phase) const;

void OnHLODObjectRegistered(IWorldPartitionHLODObject* HLODObject);

// Funções auxiliares robustas para Sanctuary Islands
FVector FindOptimalSanctuaryIslandLocation() const;
EAURACRONEnvironmentType DetermineOptimalEnvironmentForSanctuary(const FVector& Location) const;
void RepositionSanctuaryIslandsForOptimalDistribution();
void CreateMissingSanctuaryIslandsPerEnvironment();
};
