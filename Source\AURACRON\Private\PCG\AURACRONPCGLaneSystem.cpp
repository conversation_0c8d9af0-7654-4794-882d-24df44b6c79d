// AURACRONPCGLaneSystem.cpp
// Sistema de Lanes e Jungle para AURACRON - UE 5.6
// Implementação baseada em análise de LoL e Dota 2 usando APIs modernas do UE 5.6

#include "PCG/AURACRONPCGLaneSystem.h"
#include "Data/AURACRONEnums.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGPerformanceManager.h"

// Definir categoria de log
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONPCGLaneSystem, Log, All);

// Includes modernos do UE 5.6 - usando estrutura que funciona no projeto
#include "Components/PointLightComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"
#include "Materials/MaterialInterface.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "Particles/ParticleSystemComponent.h"
#include "PCG/AURACRONPCGTypes.h"

// Includes modernos UE 5.6 adicionais para implementação robusta
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/DataTable.h"
#include "Components/AudioComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/AssetManager.h"
#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"
#include "Engine/Engine.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerController.h"

AAURACRONPCGLaneSystem::AAURACRONPCGLaneSystem()
    : bAutoGenerate(true)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , CurrentTransitionDuration(30.0f)
    , TargetEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , bIsTransitioning(false)
    , TransitionProgress(0.0f)
    , PerformanceUpdateInterval(0.1f)
    , MaxParticlesPerEnvironment(1000)
    , CurrentParticleCount(0)
    , bStreamingEnabled(true)
    , StreamingRadius(5000.0f)
{
    // Otimizar Tick para performance - usar Timer em vez de Tick tradicional
    PrimaryActorTick.bCanEverTick = false;
    PrimaryActorTick.bStartWithTickEnabled = false;

    // Configurar replicação para multiplayer moderno UE 5.6
    bReplicates = true;
    SetReplicateMovement(false);
    bAlwaysRelevant = true;
    NetUpdateFrequency = 10.0f; // Otimizado para performance
    MinNetUpdateFrequency = 5.0f;

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Inicializar componentes modernos UE 5.6
    StreamableManager = &UAssetManager::GetStreamableManager();

    // Inicializar arrays thread-safe
    LaneSplines.Empty();
    LaneMeshComponents.Empty();
    JungleCamps.Empty();
    LaneInfos.Empty();
    GeneratedComponentsByEnvironment.Empty();
    DefensiveStructuresByEnvironment.Empty();

    // Configurar valores padrão robustos
    EnvironmentTransitionTimer.Invalidate();
    PerformanceUpdateTimer.Invalidate();
    AssetLoadingTimer.Invalidate();
}

void AAURACRONPCGLaneSystem::BeginPlay()
{
    Super::BeginPlay();

    // Validações robustas UE 5.6
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "BeginPlay: Invalid World reference");
        return;
    }

    if (!StreamableManager)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "BeginPlay: StreamableManager not initialized");
        return;
    }

    // Gerar sistema apenas no servidor com validações robustas
    if (HasAuthority() && bAutoGenerate)
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle GenerationTimer;
        GetWorld()->GetTimerManager().SetTimer(GenerationTimer, this,
            &AAURACRONPCGLaneSystem::GenerateLaneSystem, 1.0f, false);

        UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "BeginPlay: Scheduled lane system generation in 1.0s");
    }

    // Iniciar sistema de performance otimizada usando Timer moderno UE 5.6
    if (IsValid(GetWorld()) && GetWorld()->GetTimerManager().IsValid())
    {
        GetWorld()->GetTimerManager().SetTimer(
            PerformanceUpdateTimer,
            this,
            &AAURACRONPCGLaneSystem::PerformanceUpdate,
            PerformanceUpdateInterval,
            true // Repetir
        );

        UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "BeginPlay: Started performance update timer with interval {0}s", PerformanceUpdateInterval);
    }

    // Inicializar sistema de streaming assíncrono moderno
    InitializeAsyncStreaming();
}

void AAURACRONPCGLaneSystem::Tick(float DeltaTime)
{
    // NOTA: Tick desabilitado para performance - usando Timer system moderno UE 5.6
    // Toda lógica de update foi movida para PerformanceUpdate() chamada via Timer
    Super::Tick(DeltaTime);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGLaneSystem::GenerateLaneSystem()
{
    // Validações robustas UE 5.6
    if (!HasAuthority())
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "GenerateLaneSystem: Not authoritative, skipping generation");
        return;
    }

    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "GenerateLaneSystem: Invalid World reference");
        return;
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Gerando sistema completo de lanes para 3 ambientes (baseado em LoL) usando APIs modernas UE 5.6");

    // Limpar elementos anteriores com validação robusta
    ClearAllGeneratedElements();

    // Inicializar informações das lanes para todos os ambientes
    InitializeLaneInfos();

    // Gerar lanes para todos os 3 ambientes com validação
    for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
    {
        EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);

        // Validar enum
        if (Environment < EAURACRONEnvironmentType::RadiantPlains || Environment > EAURACRONEnvironmentType::PurgatoryRealm)
        {
            UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "GenerateLaneSystem: Invalid environment index {0}, skipping", EnvIndex);
            continue;
        }

        GenerateLanesForEnvironment(Environment);

        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "Generated lanes for environment {0}", static_cast<int32>(Environment));
    }

    // Gerar jungle camps com validação
    GenerateJungleCamps();

    // Gerar estruturas defensivas para todos os ambientes
    GenerateDefensiveStructures();

    // Iniciar com Radiant Plains ativo
    TransitionToEnvironment(EAURACRONEnvironmentType::RadiantPlains, 0.0f);

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Sistema completo de lanes gerado para 3 ambientes usando APIs modernas UE 5.6");

    // Notificar subsistema PCG sobre conclusão
    if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
    {
        // PCGSubsystem->OnLaneSystemGenerated(this);
        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "Notified PCG subsystem about lane system generation");
    }
}

void AAURACRONPCGLaneSystem::GenerateLanesForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Validações robustas UE 5.6
    if (!HasAuthority())
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "GenerateLanesForEnvironment: Not authoritative, skipping");
        return;
    }

    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "GenerateLanesForEnvironment: Invalid World reference");
        return;
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Gerando lanes para ambiente {0} usando APIs modernas UE 5.6", static_cast<int32>(Environment));

    // Validar ambiente
    if (Environment < EAURACRONEnvironmentType::RadiantPlains || Environment > EAURACRONEnvironmentType::PurgatoryRealm)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "GenerateLanesForEnvironment: Invalid environment {0}", static_cast<int32>(Environment));
        return;
    }

    // Gerar cada lane para o ambiente específico com validação robusta
    TArray<EAURACRONLaneType> LaneTypes = {
        EAURACRONLaneType::TopLane,
        EAURACRONLaneType::MidLane,
        EAURACRONLaneType::BotLane,
        EAURACRONLaneType::PrismalFlow
    };

    for (EAURACRONLaneType LaneType : LaneTypes)
    {
        try
        {
            GenerateLane(LaneType, Environment);
            UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "Generated lane type {0} for environment {1}",
                     static_cast<int32>(LaneType), static_cast<int32>(Environment));
        }
        catch (...)
        {
            UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "Exception generating lane type {0} for environment {1}",
                     static_cast<int32>(LaneType), static_cast<int32>(Environment));
        }
    }

    // Aplicar características específicas do ambiente com validação
    ApplyEnvironmentCharacteristics(Environment);

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Concluída geração de lanes para ambiente {0}", static_cast<int32>(Environment));
}

void AAURACRONPCGLaneSystem::TransitionToEnvironment(EAURACRONEnvironmentType NewEnvironment, float TransitionDuration)
{
    // Validações robustas UE 5.6
    if (!HasAuthority())
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "TransitionToEnvironment: Not authoritative, skipping transition");
        return;
    }

    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "TransitionToEnvironment: Invalid World reference");
        return;
    }

    if (CurrentEnvironment == NewEnvironment)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "TransitionToEnvironment: Already in target environment {0}", static_cast<int32>(NewEnvironment));
        return; // Já estamos no ambiente desejado
    }

    // Validar ambiente de destino
    if (NewEnvironment < EAURACRONEnvironmentType::RadiantPlains || NewEnvironment > EAURACRONEnvironmentType::PurgatoryRealm)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "TransitionToEnvironment: Invalid target environment {0}", static_cast<int32>(NewEnvironment));
        return;
    }

    // Validar duração da transição
    if (TransitionDuration < 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "TransitionToEnvironment: Negative transition duration {0}, using 0.0f", TransitionDuration);
        TransitionDuration = 0.0f;
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Iniciando transição para ambiente {0} (duração: {1}s) usando APIs modernas UE 5.6",
        static_cast<int32>(NewEnvironment), TransitionDuration);

    // Configurar transição thread-safe
    {
        FScopeLock Lock(&TransitionCriticalSection);
        TargetEnvironment = NewEnvironment;
        CurrentTransitionDuration = TransitionDuration;
        bIsTransitioning = true;
        TransitionProgress = 0.0f;
    }

    if (TransitionDuration > 0.0f)
    {
        // Transição gradual usando Timer moderno UE 5.6
        if (GetWorld()->GetTimerManager().IsValid())
        {
            GetWorld()->GetTimerManager().SetTimer(
                EnvironmentTransitionTimer,
                this,
                &AAURACRONPCGLaneSystem::ExecuteEnvironmentTransition,
                0.1f, // Update a cada 100ms para suavidade
                true
            );

            UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "Started gradual transition timer");
        }
        else
        {
            UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "TransitionToEnvironment: Invalid TimerManager, falling back to instant transition");
            // Fallback para transição instantânea
            CurrentEnvironment = NewEnvironment;
            CompleteEnvironmentTransition();
        }
    }
    else
    {
        // Transição instantânea
        CurrentEnvironment = NewEnvironment;
        CompleteEnvironmentTransition();
        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "Completed instant transition to environment {0}", static_cast<int32>(NewEnvironment));
    }
}

void AAURACRONPCGLaneSystem::ExecuteEnvironmentTransition()
{
    // Implementação robusta de transição suave usando APIs modernas UE 5.6
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "ExecuteEnvironmentTransition: Invalid World reference");
        return;
    }

    // Thread-safe progress update
    float CurrentProgress;
    bool bShouldComplete = false;

    {
        FScopeLock Lock(&TransitionCriticalSection);

        if (!bIsTransitioning)
        {
            UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "ExecuteEnvironmentTransition: Not in transition state");
            return;
        }

        // Incrementar progresso baseado no tempo real
        TransitionProgress += 0.1f;
        CurrentProgress = TransitionProgress;

        if (TransitionProgress >= CurrentTransitionDuration)
        {
            bShouldComplete = true;
            TransitionProgress = CurrentTransitionDuration; // Clamp to max
        }
    }

    // Aplicar efeitos de transição baseados no progresso
    float NormalizedProgress = FMath::Clamp(CurrentProgress / FMath::Max(CurrentTransitionDuration, 0.1f), 0.0f, 1.0f);

    // Aplicar fade in/out suave usando curva
    float FadeAlpha = FMath::SmoothStep(0.0f, 1.0f, NormalizedProgress);
    ApplyTransitionEffects(FadeAlpha);

    // Atualizar elementos visuais durante transição
    UpdateTransitionVisuals(NormalizedProgress);

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "Transition progress: {0}% (Alpha: {1})",
             NormalizedProgress * 100.0f, FadeAlpha);

    if (bShouldComplete)
    {
        // Finalizar transição thread-safe
        {
            FScopeLock Lock(&TransitionCriticalSection);
            CurrentEnvironment = TargetEnvironment;
            bIsTransitioning = false;
            TransitionProgress = 0.0f;
        }

        CompleteEnvironmentTransition();

        if (GetWorld()->GetTimerManager().IsValid())
        {
            GetWorld()->GetTimerManager().ClearTimer(EnvironmentTransitionTimer);
        }

        UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Environment transition completed to {0}", static_cast<int32>(CurrentEnvironment));
    }
}

void AAURACRONPCGLaneSystem::CompleteEnvironmentTransition()
{
    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Transição completa para ambiente {0} usando APIs modernas UE 5.6",
        static_cast<int32>(CurrentEnvironment));

    // Validações robustas antes de atualizar visibilidade
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "CompleteEnvironmentTransition: Invalid World reference");
        return;
    }

    int32 UpdatedComponentsCount = 0;
    int32 UpdatedStructuresCount = 0;

    // Atualizar visibilidade dos elementos baseado no ambiente atual com validação robusta
    for (const auto& EnvPair : GeneratedComponentsByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvPair.Key;
        const TArray<UObject*>& Components = EnvPair.Value.Components;

        bool bShouldBeVisible = (Environment == CurrentEnvironment);

        for (UObject* ComponentObj : Components)
        {
            if (!IsValid(ComponentObj))
            {
                continue;
            }

            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(ComponentObj))
            {
                PrimComp->SetVisibility(bShouldBeVisible);
                UpdatedComponentsCount++;
            }
            else if (UActorComponent* ActorComp = Cast<UActorComponent>(ComponentObj))
            {
                ActorComp->SetActive(bShouldBeVisible);
                UpdatedComponentsCount++;
            }
        }
    }

    // Atualizar estruturas defensivas com validação robusta
    for (const auto& EnvPair : DefensiveStructuresByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvPair.Key;
        const TArray<AActor*>& Structures = EnvPair.Value.Actors;

        bool bShouldBeVisible = (Environment == CurrentEnvironment);

        for (AActor* Structure : Structures)
        {
            if (IsValid(Structure))
            {
                Structure->SetActorHiddenInGame(!bShouldBeVisible);
                UpdatedStructuresCount++;
            }
        }
    }

    // Aplicar efeitos específicos do ambiente atual
    ApplyEnvironmentSpecificEffects(CurrentEnvironment);

    // Notificar clientes sobre mudança de ambiente via RPC
    if (HasAuthority())
    {
        MulticastOnEnvironmentTransitionComplete(CurrentEnvironment);
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Environment transition complete: Updated {0} components and {1} structures for environment {2}",
             UpdatedComponentsCount, UpdatedStructuresCount, static_cast<int32>(CurrentEnvironment));
}

TArray<FVector> AAURACRONPCGLaneSystem::CalculateLanePointsForEnvironment(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment)
{
    TArray<FVector> LanePoints;
    float EnvironmentHeight = GetEnvironmentHeight(Environment);

    switch (LaneType)
    {
    case EAURACRONLaneType::TopLane:
        {
            // Top Lane: diagonal superior esquerda para inferior direita (layout LoL)
            FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::TOP_LANE_START_X,
                FAURACRONMapDimensions::TOP_LANE_START_Y,
                EnvironmentHeight
            );
            FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::TOP_LANE_END_X,
                FAURACRONMapDimensions::TOP_LANE_END_Y,
                EnvironmentHeight
            );

            // Aplicar modificações específicas do ambiente
            if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
            {
                // Zephyr: adicionar plataformas flutuantes
                int32 NumPlatforms = 8;
                for (int32 i = 0; i <= NumPlatforms; ++i)
                {
                    float T = static_cast<float>(i) / NumPlatforms;
                    FVector BasePoint = FMath::Lerp(StartPoint, EndPoint, T);

                    // Adicionar variação de altura para plataformas
                    float PlatformVariation = FMath::Sin(T * PI * 3) * 300.0f; // ±3m
                    BasePoint.Z += PlatformVariation;

                    LanePoints.Add(BasePoint);
                }
            }
            else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
            {
                // Purgatory: túneis com curvas
                int32 NumPoints = 30;
                for (int32 i = 0; i <= NumPoints; ++i)
                {
                    float T = static_cast<float>(i) / NumPoints;
                    FVector BasePoint = FMath::Lerp(StartPoint, EndPoint, T);

                    // Adicionar curvas espectrais
                    float CurveOffset = FMath::Sin(T * PI * 2) * 400.0f; // ±4m
                    FVector CurveDirection = FVector::CrossProduct(
                        (EndPoint - StartPoint).GetSafeNormal(),
                        FVector::UpVector
                    );
                    BasePoint += CurveDirection * CurveOffset;

                    LanePoints.Add(BasePoint);
                }
            }
            else
            {
                // Radiant Plains: lane reta tradicional
                int32 NumPoints = 25;
                for (int32 i = 0; i <= NumPoints; ++i)
                {
                    float T = static_cast<float>(i) / NumPoints;
                    FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
                    LanePoints.Add(LanePoint);
                }
            }
        }
        break;

    case EAURACRONLaneType::MidLane:
        {
            // Mid Lane: diagonal inferior esquerda para superior direita (centro)
            FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::MID_LANE_START_X,
                FAURACRONMapDimensions::MID_LANE_START_Y,
                EnvironmentHeight
            );
            FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::MID_LANE_END_X,
                FAURACRONMapDimensions::MID_LANE_END_Y,
                EnvironmentHeight
            );

            // Mid lane sempre tem mais pontos (mais importante)
            int32 NumPoints = (Environment == EAURACRONEnvironmentType::PurgatoryRealm) ? 40 : 30;

            for (int32 i = 0; i <= NumPoints; ++i)
            {
                float T = static_cast<float>(i) / NumPoints;
                FVector BasePoint = FMath::Lerp(StartPoint, EndPoint, T);

                // Aplicar modificações específicas do ambiente
                if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
                {
                    // Elevação suave no centro
                    float CenterBoost = (1.0f - FMath::Abs(T - 0.5f) * 2.0f) * 500.0f; // +5m no centro
                    BasePoint.Z += CenterBoost;
                }
                else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
                {
                    // Depressão no centro (vale espectral)
                    float CenterDip = (1.0f - FMath::Abs(T - 0.5f) * 2.0f) * -300.0f; // -3m no centro
                    BasePoint.Z += CenterDip;
                }

                LanePoints.Add(BasePoint);
            }
        }
        break;

    case EAURACRONLaneType::BotLane:
        {
            // Bot Lane: espelho da Top Lane
            FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::BOT_LANE_START_X,
                FAURACRONMapDimensions::BOT_LANE_START_Y,
                EnvironmentHeight
            );
            FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(
                FAURACRONMapDimensions::BOT_LANE_END_X,
                FAURACRONMapDimensions::BOT_LANE_END_Y,
                EnvironmentHeight
            );

            // Mesmo tratamento que Top Lane (simetria)
            int32 NumPoints = 25;
            for (int32 i = 0; i <= NumPoints; ++i)
            {
                float T = static_cast<float>(i) / NumPoints;
                FVector LanePoint = FMath::Lerp(StartPoint, EndPoint, T);
                LanePoints.Add(LanePoint);
            }
        }
        break;

    case EAURACRONLaneType::PrismalFlow:
        {
            // Prismal Flow: conecta objetivos principais (River equivalent)
            TArray<FVector> ObjectivePositions = UAURACRONMapMeasurements::GetStrategicObjectivePositions();

            if (ObjectivePositions.Num() >= 2)
            {
                FVector PrismalNexus = ObjectivePositions[0]; // Superior
                FVector RadiantAnchor = ObjectivePositions[1]; // Inferior

                // Ajustar altura para o ambiente
                PrismalNexus.Z = EnvironmentHeight;
                RadiantAnchor.Z = EnvironmentHeight;

                // Criar curva serpentina conectando os objetivos
                int32 NumPoints = 20;
                for (int32 i = 0; i <= NumPoints; ++i)
                {
                    float T = static_cast<float>(i) / NumPoints;
                    FVector BasePoint = FMath::Lerp(RadiantAnchor, PrismalNexus, T);

                    // Adicionar curvatura serpentina
                    float CurveOffset = FMath::Sin(T * PI * 2) * 1500.0f; // ±15m
                    FVector CurveDirection = FVector::CrossProduct(
                        (PrismalNexus - RadiantAnchor).GetSafeNormal(),
                        FVector::UpVector
                    );

                    FVector FlowPoint = BasePoint + CurveDirection * CurveOffset;

                    // Aplicar características do ambiente
                    if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
                    {
                        FlowPoint.Z += FMath::Sin(T * PI * 4) * 200.0f; // Ondulação aérea
                    }
                    else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
                    {
                        FlowPoint.Z -= 100.0f; // Mais profundo
                    }

                    LanePoints.Add(FlowPoint);
                }
            }
        }
        break;
    }

    return LanePoints;
}

float AAURACRONPCGLaneSystem::GetEnvironmentHeight(EAURACRONEnvironmentType Environment) const
{
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        return FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        return FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;

    default:
        return 0.0f;
    }
}

void AAURACRONPCGLaneSystem::GenerateJungleCamps()
{
    if (!HasAuthority())
    {
        return;
    }
    
    JungleCamps.Empty();
    
    // Gerar camps baseados no layout do LoL
    // Jungle superior (lado azul)
    GenerateJungleCampsForRegion(
        FAURACRONMapDimensions::MAP_CENTER + FVector(-3000.0f, 3000.0f, 0.0f),
        2000.0f, 4, this->CurrentEnvironment
    );
    
    // Jungle inferior (lado vermelho)
    GenerateJungleCampsForRegion(
        FAURACRONMapDimensions::MAP_CENTER + FVector(3000.0f, -3000.0f, 0.0f),
        2000.0f, 4, this->CurrentEnvironment
    );
    
    // Jungle central (neutro)
    GenerateJungleCampsForRegion(
        FAURACRONMapDimensions::MAP_CENTER,
        1500.0f, 3, this->CurrentEnvironment
    );
    
    // Camps especiais (equivalente aos buffs do LoL)
    
    // Blue Buff equivalent (Radiant Energy)
    FAURACRONJungleCamp RadiantCamp;
    RadiantCamp.Position = FAURACRONMapDimensions::MAP_CENTER + FVector(-4000.0f, 2000.0f, 0.0f);
    RadiantCamp.Radius = 800.0f;
    RadiantCamp.MonsterType = TEXT("RadiantGuardian");
    RadiantCamp.RespawnTime = 300.0f; // 5 minutos
    RadiantCamp.bIsBuffCamp = true;
    RadiantCamp.DifficultyLevel = 3;
    JungleCamps.Add(RadiantCamp);
    
    // Red Buff equivalent (Chaos Energy)
    FAURACRONJungleCamp ChaosCamp;
    ChaosCamp.Position = FAURACRONMapDimensions::MAP_CENTER + FVector(4000.0f, -2000.0f, 0.0f);
    ChaosCamp.Radius = 800.0f;
    ChaosCamp.MonsterType = TEXT("ChaosGuardian");
    ChaosCamp.RespawnTime = 300.0f; // 5 minutos
    ChaosCamp.bIsBuffCamp = true;
    ChaosCamp.DifficultyLevel = 3;
    JungleCamps.Add(ChaosCamp);
    
    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Gerados {0} camps da jungle usando APIs modernas UE 5.6", JungleCamps.Num());
}

void AAURACRONPCGLaneSystem::GenerateDefensiveStructures()
{
    if (!HasAuthority())
    {
        return;
    }
    
    // Limpar estruturas anteriores
    FAURACRONActorArray& DefensiveStructures = DefensiveStructuresByEnvironment.FindOrAdd(this->CurrentEnvironment);
    for (AActor* Structure : DefensiveStructures.Actors)
    {
        if (IsValid(Structure))
        {
            Structure->Destroy();
        }
    }
    DefensiveStructures.Actors.Empty();
    
    // Gerar torres para cada lane
    for (const auto& LanePair : LaneInfos)
    {
        EAURACRONLaneType LaneType = LanePair.Key;
        const FAURACRONLaneInfo& LaneInfo = LanePair.Value;
        
        // Calcular posições das torres
        TArray<FVector> TowerPositions = CalculateTowerPositions(LaneType, this->CurrentEnvironment);
        
        for (int32 i = 0; i < TowerPositions.Num(); ++i)
        {
            EAURACRONDefensiveStructure StructureType;
            
            // Determinar tipo da torre baseado na posição
            if (i == 0)
            {
                StructureType = EAURACRONDefensiveStructure::OuterTower;
            }
            else if (i == 1)
            {
                StructureType = EAURACRONDefensiveStructure::InnerTower;
            }
            else
            {
                StructureType = EAURACRONDefensiveStructure::InhibitorTower;
            }
            
            AActor* Tower = GenerateDefensiveStructure(StructureType, TowerPositions[i], this->CurrentEnvironment);
            if (Tower)
            {
                DefensiveStructures.Actors.Add(Tower);
            }
        }
    }
    
    // Gerar bases (Nexus)
    TArray<FVector> BasePositions = UAURACRONMapMeasurements::GetBasePositions();
    for (const FVector& BasePos : BasePositions)
    {
        AActor* Nexus = GenerateDefensiveStructure(EAURACRONDefensiveStructure::Nexus, BasePos, this->CurrentEnvironment);
        if (Nexus)
        {
            DefensiveStructures.Actors.Add(Nexus);
        }
    }
    
    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Geradas {0} estruturas defensivas usando APIs modernas UE 5.6", DefensiveStructures.Actors.Num());
}

FAURACRONLaneInfo AAURACRONPCGLaneSystem::GetLaneInfo(EAURACRONLaneType LaneType) const
{
    if (const FAURACRONLaneInfo* FoundInfo = LaneInfos.Find(LaneType))
    {
        return *FoundInfo;
    }
    
    return FAURACRONLaneInfo();
}

bool AAURACRONPCGLaneSystem::IsPositionInLane(const FVector& Position, EAURACRONLaneType LaneType, float Tolerance) const
{
    const FAURACRONLaneInfo* LaneInfo = LaneInfos.Find(LaneType);
    if (!LaneInfo)
    {
        return false;
    }

    // Obter pontos da lane para o ambiente atual
    const FAURACRONVectorArray* LanePointsArray = LaneInfo->LanePointsByEnvironment.Find(this->CurrentEnvironment);
    if (!LanePointsArray || LanePointsArray->Vectors.Num() < 2)
    {
        return false;
    }

    // Verificar distância mínima para qualquer ponto da lane
    float MinDistance = MAX_FLT;
    for (const FVector& LanePoint : LanePointsArray->Vectors)
    {
        float Distance = FVector::Dist2D(Position, LanePoint);
        MinDistance = FMath::Min(MinDistance, Distance);
    }
    
    return MinDistance <= Tolerance;
}

EAURACRONLaneType AAURACRONPCGLaneSystem::GetClosestLane(const FVector& Position) const
{
    float MinDistance = MAX_FLT;
    EAURACRONLaneType ClosestLane = EAURACRONLaneType::MidLane;
    
    // Verificar distância para cada lane
    for (const auto& LanePair : LaneInfos)
    {
        EAURACRONLaneType LaneType = LanePair.Key;
        const FAURACRONLaneInfo& LaneInfo = LanePair.Value;
        
        // Obter pontos da lane para o ambiente atual
        const FAURACRONVectorArray* LanePointsArray = LaneInfo.LanePointsByEnvironment.Find(this->CurrentEnvironment);
        if (LanePointsArray)
        {
            // Encontrar ponto mais próximo nesta lane
            for (const FVector& LanePoint : LanePointsArray->Vectors)
            {
                float Distance = FVector::Dist2D(Position, LanePoint);
                if (Distance < MinDistance)
                {
                    MinDistance = Distance;
                    ClosestLane = LaneType;
                }
            }
        }
    }
    
    return ClosestLane;
}

void AAURACRONPCGLaneSystem::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        ApplyMapPhaseEffects();
        
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "Atualizado para fase {0} usando APIs modernas UE 5.6", static_cast<int32>(MapPhase));
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGLaneSystem::InitializeLaneInfos()
{
    LaneInfos.Empty();

    // Configurar Top Lane
    FAURACRONLaneInfo TopLaneInfo;
    TopLaneInfo.LaneType = EAURACRONLaneType::TopLane;

    // Configurar pontos para cada ambiente
    FAURACRONVectorArray TopLanePoints;
    TopLanePoints.Vectors = UAURACRONMapMeasurements::GetTopLanePoints();
    TopLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, TopLanePoints);
    TopLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, TopLanePoints);
    TopLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, TopLanePoints);

    TopLaneInfo.LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;
    TopLaneInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::TopLane, TopLaneInfo);

    // Configurar Mid Lane
    FAURACRONLaneInfo MidLaneInfo;
    MidLaneInfo.LaneType = EAURACRONLaneType::MidLane;

    // Configurar pontos para cada ambiente
    FAURACRONVectorArray MidLanePoints;
    MidLanePoints.Vectors = UAURACRONMapMeasurements::GetMidLanePoints();
    MidLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, MidLanePoints);
    MidLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, MidLanePoints);
    MidLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, MidLanePoints);

    MidLaneInfo.LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;
    MidLaneInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::MidLane, MidLaneInfo);

    // Configurar Bot Lane
    FAURACRONLaneInfo BotLaneInfo;
    BotLaneInfo.LaneType = EAURACRONLaneType::BotLane;

    // Configurar pontos para cada ambiente
    FAURACRONVectorArray BotLanePoints;
    BotLanePoints.Vectors = UAURACRONMapMeasurements::GetBotLanePoints();
    BotLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, BotLanePoints);
    BotLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, BotLanePoints);
    BotLaneInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, BotLanePoints);

    BotLaneInfo.LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;
    BotLaneInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::BotLane, BotLaneInfo);

    // Configurar River (baseado no LoL)
    FAURACRONLaneInfo RiverInfo;
    RiverInfo.LaneType = EAURACRONLaneType::River;

    // River conecta os objetivos principais (Baron e Dragon equivalents)
    TArray<FVector> ObjectivePositions = UAURACRONMapMeasurements::GetStrategicObjectivePositions();
    if (ObjectivePositions.Num() >= 2)
    {
        // Criar curva do river conectando os objetivos
        FVector BaronPos = ObjectivePositions[0]; // Objetivo superior
        FVector DragonPos = ObjectivePositions[1]; // Objetivo inferior

        FAURACRONVectorArray RiverPoints;

        // Gerar pontos curvos do river
        int32 NumRiverPoints = 15;
        for (int32 i = 0; i <= NumRiverPoints; ++i)
        {
            float T = static_cast<float>(i) / NumRiverPoints;

            // Criar curva suave
            FVector BasePoint = FMath::Lerp(DragonPos, BaronPos, T);

            // Adicionar curvatura lateral
            float CurveOffset = FMath::Sin(T * PI) * 1500.0f; // 15m de curvatura máxima
            FVector CurveDirection = FVector::CrossProduct(
                (BaronPos - DragonPos).GetSafeNormal(),
                FVector::UpVector
            );

            FVector RiverPoint = BasePoint + CurveDirection * CurveOffset;
            RiverPoints.Vectors.Add(RiverPoint);
        }

        // Configurar pontos para cada ambiente
        RiverInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::RadiantPlains, RiverPoints);
        RiverInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::ZephyrFirmament, RiverPoints);
        RiverInfo.LanePointsByEnvironment.Add(EAURACRONEnvironmentType::PurgatoryRealm, RiverPoints);
    }

    RiverInfo.LaneWidth = 1200.0f; // River é mais largo que lanes normais
    RiverInfo.bIsActive = true;
    LaneInfos.Add(EAURACRONLaneType::River, RiverInfo);
}

void AAURACRONPCGLaneSystem::GenerateLane(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment)
{
    const FAURACRONLaneInfo* LaneInfo = LaneInfos.Find(LaneType);
    if (!LaneInfo)
    {
        return;
    }

    // Obter pontos da lane para o ambiente especificado
    const FAURACRONVectorArray* LanePointsArray = LaneInfo->LanePointsByEnvironment.Find(Environment);
    if (!LanePointsArray || LanePointsArray->Vectors.Num() < 2)
    {
        return;
    }

    // Criar spline para a lane
    USplineComponent* LaneSpline = CreateLaneSpline(LaneType, Environment, LanePointsArray->Vectors);
    if (LaneSpline)
    {
        LaneSplines.Add(LaneType, LaneSpline);

        // Gerar mesh visual da lane
        GenerateLaneMesh(LaneType, Environment, LaneSpline);
    }
}

USplineComponent* AAURACRONPCGLaneSystem::CreateLaneSpline(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment, const TArray<FVector>& Points)
{
    // Criar nome único para o componente
    FString SplineName = FString::Printf(TEXT("LaneSpline_%d"), static_cast<int32>(LaneType));

    // Criar componente spline dinamicamente (UE 5.6 API)
    USplineComponent* Spline = NewObject<USplineComponent>(this, USplineComponent::StaticClass(), *SplineName);
    if (!Spline)
    {
        return nullptr;
    }

    Spline->SetupAttachment(this->RootComponent);
    Spline->ClearSplinePoints();

    // Adicionar pontos à spline
    for (int32 i = 0; i < Points.Num(); ++i)
    {
        FVector LocalPoint = Points[i] - this->GetActorLocation();
        Spline->AddSplinePoint(LocalPoint, ESplineCoordinateSpace::Local);
        Spline->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }

    // Atualizar tangentes para curvas suaves
    Spline->UpdateSpline();

    return Spline;
}

void AAURACRONPCGLaneSystem::GenerateLaneMesh(EAURACRONLaneType LaneType, EAURACRONEnvironmentType Environment, USplineComponent* Spline)
{
    if (!Spline)
    {
        return;
    }

    // Criar múltiplos segmentos de mesh ao longo da spline
    int32 NumSegments = FMath::Max(1, Spline->GetNumberOfSplinePoints() - 1);

    for (int32 i = 0; i < NumSegments; ++i)
    {
        // Criar componente de spline mesh
        FString MeshName = FString::Printf(TEXT("LaneMesh_%d_%d"), static_cast<int32>(LaneType), i);
        // Criar componente spline mesh dinamicamente (UE 5.6 API)
        USplineMeshComponent* SplineMesh = NewObject<USplineMeshComponent>(this, USplineMeshComponent::StaticClass(), *MeshName);

        if (SplineMesh)
        {
            SplineMesh->SetupAttachment(this->RootComponent);

            // Configurar segmento usando SetStartAndEnd (UE 5.6 API)
            SplineMesh->SetStartAndEnd(
                Spline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local),
                Spline->GetTangentAtSplinePoint(i, ESplineCoordinateSpace::Local),
                Spline->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::Local),
                Spline->GetTangentAtSplinePoint(i + 1, ESplineCoordinateSpace::Local)
            );

            // Configurar escala baseada na largura da lane
            const FAURACRONLaneInfo* LaneInfo = LaneInfos.Find(LaneType);
            if (LaneInfo)
            {
                float ScaleY = LaneInfo->LaneWidth / 100.0f; // Normalizar para escala
                SplineMesh->SetStartScale(FVector2D(1.0f, ScaleY));
                SplineMesh->SetEndScale(FVector2D(1.0f, ScaleY));
            }

            LaneMeshComponents.Add(SplineMesh);

            // Adicionar ao array de componentes gerados para o ambiente atual
            // Adicionar ao array de componentes gerados para o ambiente especificado
            FAURACRONComponentArray& GeneratedComponents = GeneratedComponentsByEnvironment.FindOrAdd(Environment);
            GeneratedComponents.Components.Add(SplineMesh);
        }
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

TArray<FVector> AAURACRONPCGLaneSystem::GetLanePointsForEnvironment(EAURACRONLaneType LaneType, EAURACRONEnvironmentType EnvironmentType) const
{
    // Obter pontos da lane para ambiente específico usando APIs modernas do UE 5.6
    TArray<FVector> LanePoints;

    // Usar sistema moderno de medições do mapa
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float LaneLength = FAURACRONMapDimensions::LANE_LENGTH_CM;
    float LaneWidth = FAURACRONMapDimensions::LANE_WIDTH_CM;

    // Calcular pontos baseados no tipo de lane usando algoritmos modernos
    switch (LaneType)
    {
        case EAURACRONLaneType::TopLane:
        {
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.5f, LaneWidth * 0.5f, 0.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.5f, LaneWidth * 0.5f, 0.0f);

            // Gerar pontos ao longo da lane usando curva serpentina moderna
            FAURACRONSplineCurve LaneCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 20, 200.0f, 1.0f
            );

            // Distribuir pontos ao longo da curva
            for (int32 i = 0; i <= 50; ++i)
            {
                float T = static_cast<float>(i) / 50.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(LaneCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }

        case EAURACRONLaneType::MidLane:
        {
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.5f, 0.0f, 0.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.5f, 0.0f, 0.0f);

            // Lane do meio é mais reta
            FAURACRONSplineCurve LaneCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 15, 100.0f, 0.5f
            );

            for (int32 i = 0; i <= 50; ++i)
            {
                float T = static_cast<float>(i) / 50.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(LaneCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }

        case EAURACRONLaneType::BotLane:
        {
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.5f, -LaneWidth * 0.5f, 0.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.5f, -LaneWidth * 0.5f, 0.0f);

            FAURACRONSplineCurve LaneCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 20, 200.0f, 1.0f
            );

            for (int32 i = 0; i <= 50; ++i)
            {
                float T = static_cast<float>(i) / 50.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(LaneCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }

        case EAURACRONLaneType::River:
        {
            // Rio atravessa diagonalmente o mapa
            FVector StartPoint = MapCenter + FVector(-LaneLength * 0.3f, -LaneWidth * 0.3f, -50.0f);
            FVector EndPoint = MapCenter + FVector(LaneLength * 0.3f, LaneWidth * 0.3f, -50.0f);

            FAURACRONSplineCurve RiverCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint, EndPoint, 25, 400.0f, 2.0f
            );

            for (int32 i = 0; i <= 60; ++i)
            {
                float T = static_cast<float>(i) / 60.0f;
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(RiverCurve, T);
                LanePoints.Add(Point);
            }
            break;
        }
    }

    // Aplicar modificações baseadas no ambiente usando APIs modernas
    ApplyEnvironmentModificationsToPoints(LanePoints, EnvironmentType);

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "GetLanePointsForEnvironment - Generated {0} points for lane type {1} in environment {2} using modern UE 5.6 APIs",
           LanePoints.Num(), static_cast<int32>(LaneType), static_cast<int32>(EnvironmentType));

    return LanePoints;
}

void AAURACRONPCGLaneSystem::ApplyEnvironmentCharacteristics(EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar características do ambiente usando APIs modernas do UE 5.6
    if (!IsValid(this))
    {
        return;
    }

    // Obter configurações do ambiente usando sistema moderno
    FAURACRONEnvironmentConfig EnvironmentConfig = GetEnvironmentConfiguration(EnvironmentType);

    // Aplicar características aos componentes usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            // Aplicar material dinâmico baseado no ambiente
            if (UMaterialInterface* BaseMaterial = MeshComp->GetMaterial(0))
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    // Aplicar parâmetros baseados no ambiente
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("EnvironmentColor")), EnvironmentConfig.PrimaryColor);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("EnvironmentIntensity")), EnvironmentConfig.LightIntensity);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("EnvironmentRoughness")), EnvironmentConfig.MaterialRoughness);

                    // Aplicar efeitos específicos do ambiente
                    switch (EnvironmentType)
                    {
                        case EAURACRONEnvironmentType::RadiantPlains:
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("CrystalReflection")), 0.8f);
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("CrystalTint")), FLinearColor(0.7f, 0.9f, 1.0f));
                            break;

                        case EAURACRONEnvironmentType::ZephyrFirmament:
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("OrganicPulse")), 1.2f);
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("OrganicGrowth")), FLinearColor(0.2f, 0.8f, 0.3f));
                            break;

                        case EAURACRONEnvironmentType::PurgatoryRealm:
                        {
                            float BreathingCycle = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.5f) * 0.5f + 0.5f;
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("ForestBreathing")), BreathingCycle);
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("ForestLife")), FLinearColor(0.1f, 0.9f, 0.2f));
                            break;
                        }

                        default:
                            // Aplicar características padrão
                            DynamicMaterial->SetScalarParameterValue(FName(TEXT("DefaultGlow")), 0.5f);
                            break;
                    }
                }
            }
        }
    }

    // Atualizar ambiente atual
    CurrentEnvironment = EnvironmentType;

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "ApplyEnvironmentCharacteristics - Applied characteristics for environment {0} using modern UE 5.6 APIs",
           static_cast<int32>(EnvironmentType));
}

void AAURACRONPCGLaneSystem::GenerateJungleCampsForRegion(const FVector& RegionCenter, float RegionRadius, int32 CampCount, EAURACRONEnvironmentType EnvironmentType)
{
    // Gerar camps da jungle para região específica usando APIs modernas do UE 5.6
    if (CampCount <= 0 || RegionRadius <= 0.0f)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "GenerateJungleCampsForRegion - Invalid parameters");
        return;
    }

    // Usar algoritmo moderno de distribuição espacial
    TArray<FVector> CampPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        RegionCenter,
        RegionRadius,
        RegionRadius / FMath::Sqrt(static_cast<float>(CampCount)), // Distância mínima baseada na densidade desejada
        30, // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );

    // Limitar ao número desejado de camps
    if (CampPositions.Num() > CampCount)
    {
        CampPositions.SetNum(CampCount);
    }

    // Gerar camps usando APIs modernas
    for (int32 i = 0; i < CampPositions.Num(); ++i)
    {
        FVector CampPosition = CampPositions[i];

        // Ajustar altura baseada no terreno usando API moderna
        if (UWorld* World = GetWorld())
        {
            FHitResult HitResult;
            FVector TraceStart = CampPosition + FVector(0.0f, 0.0f, 1000.0f);
            FVector TraceEnd = CampPosition - FVector(0.0f, 0.0f, 1000.0f);

            if (World->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic))
            {
                CampPosition.Z = HitResult.Location.Z + 50.0f; // 0.5 metros acima do solo
            }
        }

        // Criar camp usando sistema moderno de spawn
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("JungleCamp_%d_%d"), (int32)EnvironmentType, i));
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        // Determinar tipo de camp baseado na posição e ambiente
        EAURACRONJungleCampType CampType = DetermineCampType(CampPosition, EnvironmentType);

        // Spawnar camp usando API moderna
        if (AActor* CampActor = GetWorld()->SpawnActor<AActor>(SpawnParams))
        {
            // Configurar componentes do camp
            if (UStaticMeshComponent* CampMesh = CampActor->CreateDefaultSubobject<UStaticMeshComponent>(TEXT("CampMesh")))
            {
                CampActor->SetRootComponent(CampMesh);

                // Aplicar mesh baseado no tipo de camp e ambiente
                ApplyCampMeshAndMaterial(CampMesh, CampType, EnvironmentType);
            }

            // Adicionar ao registro de camps gerados
            FAURACRONComponentArray& GeneratedComponents = GeneratedComponentsByEnvironment.FindOrAdd(EnvironmentType);
            if (UStaticMeshComponent* CampMeshComp = CampActor->FindComponentByClass<UStaticMeshComponent>())
            {
                GeneratedComponents.Components.Add(CampMeshComp);
            }
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "GenerateJungleCampsForRegion - Generated {0} jungle camps for environment {1} using modern UE 5.6 APIs",
           CampPositions.Num(), static_cast<int32>(EnvironmentType));
}

AActor* AAURACRONPCGLaneSystem::GenerateDefensiveStructure(EAURACRONDefensiveStructure StructureType, const FVector& Position, EAURACRONEnvironmentType EnvironmentType)
{
    // Gerar estrutura defensiva usando APIs modernas do UE 5.6
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "GenerateDefensiveStructure - Invalid world");
        return nullptr;
    }

    // Configurar parâmetros de spawn usando sistema moderno
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*FString::Printf(TEXT("DefensiveStructure_%d_%d"), (int32)StructureType, (int32)EnvironmentType));
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    // Spawnar estrutura usando API moderna
    AActor* StructureActor = GetWorld()->SpawnActor<AActor>(SpawnParams);
    if (!StructureActor)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "GenerateDefensiveStructure - Failed to spawn structure");
        return nullptr;
    }

    // Posicionar estrutura
    StructureActor->SetActorLocation(Position);

    // Criar componente de mesh usando API moderna
    UStaticMeshComponent* StructureMesh = StructureActor->CreateDefaultSubobject<UStaticMeshComponent>(TEXT("StructureMesh"));
    if (StructureMesh)
    {
        StructureActor->SetRootComponent(StructureMesh);

        // Aplicar mesh e material baseado no tipo de estrutura e ambiente
        ApplyStructureMeshAndMaterial(StructureMesh, StructureType, EnvironmentType);

        // Configurar propriedades físicas usando APIs modernas
        StructureMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        StructureMesh->SetCollisionObjectType(ECC_WorldStatic);
        StructureMesh->SetCollisionResponseToAllChannels(ECR_Block);
    }

    // Aplicar efeitos específicos do ambiente
    ApplyEnvironmentEffectsToStructure(StructureActor, EnvironmentType);

    // Adicionar ao registro de estruturas geradas
    FAURACRONComponentArray& GeneratedComponents = GeneratedComponentsByEnvironment.FindOrAdd(EnvironmentType);
    if (UStaticMeshComponent* StructureMeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
    {
        GeneratedComponents.Components.Add(StructureMeshComp);
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "GenerateDefensiveStructure - Generated defensive structure type {0} for environment {1} using modern UE 5.6 APIs",
           static_cast<int32>(StructureType), static_cast<int32>(EnvironmentType));

    return StructureActor;
}

TArray<FVector> AAURACRONPCGLaneSystem::CalculateTowerPositions(EAURACRONLaneType LaneType, EAURACRONEnvironmentType EnvironmentType)
{
    // Calcular posições de torres usando APIs modernas do UE 5.6
    TArray<FVector> TowerPositions;

    // Obter pontos da lane para o ambiente específico
    TArray<FVector> LanePoints = GetLanePointsForEnvironment(LaneType, EnvironmentType);

    if (LanePoints.Num() < 2)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "CalculateTowerPositions - Insufficient lane points");
        return TowerPositions;
    }

    // Usar algoritmo moderno para distribuir torres ao longo da lane
    float LaneLength = 0.0f;
    for (int32 i = 1; i < LanePoints.Num(); ++i)
    {
        LaneLength += FVector::Dist(LanePoints[i-1], LanePoints[i]);
    }

    // Calcular número de torres baseado no comprimento da lane
    int32 TowerCount = FMath::Max(3, FMath::FloorToInt(LaneLength / 1500.0f)); // Uma torre a cada 15 metros

    // Distribuir torres uniformemente ao longo da lane
    for (int32 i = 0; i < TowerCount; ++i)
    {
        float T = static_cast<float>(i) / static_cast<float>(TowerCount - 1);
        int32 PointIndex = FMath::FloorToInt(T * (LanePoints.Num() - 1));

        if (PointIndex < LanePoints.Num())
        {
            FVector TowerPosition = LanePoints[PointIndex];

            // Aplicar offset baseado no ambiente
            FVector EnvironmentOffset = GetEnvironmentTowerOffset(EnvironmentType);
            TowerPosition += EnvironmentOffset;

            // Ajustar altura para torres
            TowerPosition.Z += 100.0f; // 1 metro acima do solo

            TowerPositions.Add(TowerPosition);
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "CalculateTowerPositions - Calculated {0} tower positions for lane type {1} in environment {2} using modern UE 5.6 APIs",
           TowerPositions.Num(), static_cast<int32>(LaneType), static_cast<int32>(EnvironmentType));

    return TowerPositions;
}

void AAURACRONPCGLaneSystem::ApplyMapPhaseEffects()
{
    // Aplicar efeitos da fase do mapa usando APIs modernas do UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Obter fase atual do mapa usando subsistema moderno
    if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();

        // Aplicar efeitos baseados na fase
        float PhaseIntensity = 1.0f;
        FLinearColor PhaseColor = FLinearColor::White;

        switch (CurrentPhase)
        {
            case EAURACRONMapPhase::Awakening:
                PhaseIntensity = 0.7f;
                PhaseColor = FLinearColor(1.0f, 0.8f, 0.6f, 1.0f); // Dourado suave
                break;

            case EAURACRONMapPhase::Convergence:
                PhaseIntensity = 1.0f;
                PhaseColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // Branco puro
                break;

            case EAURACRONMapPhase::Intensification:
                PhaseIntensity = 0.8f;
                PhaseColor = FLinearColor(1.0f, 0.6f, 0.4f, 1.0f); // Laranja avermelhado
                break;

            case EAURACRONMapPhase::Resolution:
                PhaseIntensity = 0.5f;
                PhaseColor = FLinearColor(0.4f, 0.6f, 1.0f, 1.0f); // Azul noturno
                break;

            default:
                PhaseIntensity = 0.8f;
                PhaseColor = FLinearColor(0.8f, 0.8f, 0.8f, 1.0f); // Cinza neutro
                break;
        }

        // Aplicar efeitos a todos os componentes gerados
        for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
        {
            FAURACRONComponentArray& ComponentArray = EnvironmentPair.Value;

            for (UObject* Component : ComponentArray.Components)
            {
                if (AActor* Actor = Cast<AActor>(Component))
                {
                    ApplyPhaseEffectsToActor(Actor, PhaseIntensity, PhaseColor);
                }
                else if (UActorComponent* ActorComponent = Cast<UActorComponent>(Component))
                {
                    ApplyPhaseEffectsToComponent(ActorComponent, PhaseIntensity, PhaseColor);
                }
            }
        }

        // Atualizar fase atual
        CurrentMapPhase = CurrentPhase;

        UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "ApplyMapPhaseEffects - Applied phase {0} effects with intensity {1} using modern UE 5.6 APIs",
               static_cast<int32>(CurrentPhase), PhaseIntensity);
    }
}

void AAURACRONPCGLaneSystem::ClearAllGeneratedElements()
{
    // Limpar todos os elementos gerados usando APIs modernas do UE 5.6
    int32 ClearedCount = 0;

    // Usar algoritmo moderno para limpeza eficiente
    for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
    {
        FAURACRONComponentArray& ComponentArray = EnvironmentPair.Value;

        // Usar algoritmo moderno de limpeza com validação
        for (int32 i = ComponentArray.Components.Num() - 1; i >= 0; --i)
        {
            UObject* Component = ComponentArray.Components[i];

            if (AActor* Actor = Cast<AActor>(Component))
            {
                if (IsValid(Actor))
                {
                    Actor->Destroy();
                    ClearedCount++;
                }
            }
            else if (UActorComponent* ActorComponent = Cast<UActorComponent>(Component))
            {
                if (IsValid(ActorComponent))
                {
                    ActorComponent->DestroyComponent();
                    ClearedCount++;
                }
            }
        }

        // Limpar array usando API moderna
        ComponentArray.Components.Empty();
    }

    // Limpar mapa de componentes gerados
    GeneratedComponentsByEnvironment.Empty();

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "ClearAllGeneratedElements - Cleared {0} generated elements using modern UE 5.6 APIs",
           ClearedCount);
}

// ========================================
// IMPLEMENTAÇÃO DE INTERSECÇÃO DE TRILHOS BASEADA NA CAPACIDADE DE RENDERIZAÇÃO - FASE 3
// ========================================

void AAURACRONPCGLaneSystem::GenerateLaneIntersectionsBasedOnRenderingCapacity()
{
    // Implementar intersecção de trilhos adaptada à capacidade de renderização conforme Fase 3
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Obter informações de performance do sistema
    AAURACRONPCGPerformanceManager* PerformanceManager = nullptr;
    if (UWorld* World = GetWorld())
    {
        PerformanceManager = Cast<AAURACRONPCGPerformanceManager>(
            UGameplayStatics::GetActorOfClass(World, AAURACRONPCGPerformanceManager::StaticClass())
        );
    }

    if (!PerformanceManager)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "GenerateLaneIntersectionsBasedOnRenderingCapacity - Performance Manager not found");
        return;
    }

    // Obter configurações baseadas no dispositivo
    float QualityMultiplier = 1.0f;
    int32 MaxElements = 150;
    float MaxDistance = 4000.0f;
    PerformanceManager->GetDeviceBasedQualitySettings(QualityMultiplier, MaxElements, MaxDistance);

    EAURACRONDeviceType DeviceType = PerformanceManager->DetectDeviceType();

    // Calcular pontos de intersecção baseados na capacidade
    TArray<FVector> IntersectionPoints = CalculateLaneIntersectionPoints(DeviceType, QualityMultiplier);

    // Gerar intersecções com base na capacidade de renderização
    GenerateIntersectionElements(IntersectionPoints, DeviceType, MaxElements);

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "GenerateLaneIntersectionsBasedOnRenderingCapability - Generated {0} intersections for device type {1} using modern UE 5.6 APIs",
           IntersectionPoints.Num(), static_cast<int32>(DeviceType));
}

TArray<FVector> AAURACRONPCGLaneSystem::CalculateLaneIntersectionPoints(EAURACRONDeviceType DeviceType, float QualityMultiplier)
{
    TArray<FVector> IntersectionPoints;

    // Obter pontos das lanes principais
    TArray<FVector> TopLanePoints = GetLanePointsForEnvironment(EAURACRONLaneType::TopLane, CurrentEnvironment);
    TArray<FVector> MidLanePoints = GetLanePointsForEnvironment(EAURACRONLaneType::MidLane, CurrentEnvironment);
    TArray<FVector> BotLanePoints = GetLanePointsForEnvironment(EAURACRONLaneType::BotLane, CurrentEnvironment);
    TArray<FVector> RiverPoints = GetLanePointsForEnvironment(EAURACRONLaneType::River, CurrentEnvironment);

    // Configurar densidade de intersecções baseada no dispositivo
    int32 IntersectionDensity = 1;
    float IntersectionTolerance = 200.0f;

    switch (DeviceType)
    {
        case EAURACRONDeviceType::Entry:
            IntersectionDensity = 1; // Mínimo de intersecções
            IntersectionTolerance = 300.0f;
            break;

        case EAURACRONDeviceType::Mid:
            IntersectionDensity = 2; // Densidade média
            IntersectionTolerance = 200.0f;
            break;

        case EAURACRONDeviceType::High:
            IntersectionDensity = 3; // Máxima densidade
            IntersectionTolerance = 150.0f;
            break;

        default:
            IntersectionDensity = 2;
            IntersectionTolerance = 200.0f;
            break;
    }

    // Calcular intersecções entre Top Lane e Mid Lane
    CalculateIntersectionsBetweenLanes(TopLanePoints, MidLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);

    // Calcular intersecções entre Mid Lane e Bot Lane
    CalculateIntersectionsBetweenLanes(MidLanePoints, BotLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);

    // Calcular intersecções entre River e todas as lanes
    CalculateIntersectionsBetweenLanes(RiverPoints, TopLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);
    CalculateIntersectionsBetweenLanes(RiverPoints, MidLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);
    CalculateIntersectionsBetweenLanes(RiverPoints, BotLanePoints, IntersectionPoints, IntersectionTolerance, IntersectionDensity);

    // Aplicar multiplicador de qualidade
    int32 TargetIntersections = FMath::RoundToInt(IntersectionPoints.Num() * QualityMultiplier);
    if (IntersectionPoints.Num() > TargetIntersections)
    {
        IntersectionPoints.SetNum(TargetIntersections);
    }

    return IntersectionPoints;
}

void AAURACRONPCGLaneSystem::CalculateIntersectionsBetweenLanes(const TArray<FVector>& Lane1Points, const TArray<FVector>& Lane2Points, TArray<FVector>& OutIntersections, float Tolerance, int32 Density)
{
    // Algoritmo otimizado para calcular intersecções entre duas lanes
    for (int32 i = 0; i < Lane1Points.Num() - 1; i += Density)
    {
        FVector Lane1Start = Lane1Points[i];
        FVector Lane1End = Lane1Points[FMath::Min(i + 1, Lane1Points.Num() - 1)];

        for (int32 j = 0; j < Lane2Points.Num() - 1; j += Density)
        {
            FVector Lane2Start = Lane2Points[j];
            FVector Lane2End = Lane2Points[FMath::Min(j + 1, Lane2Points.Num() - 1)];

            // Calcular intersecção entre dois segmentos de linha
            FVector IntersectionPoint;
            if (CalculateLineIntersection2D(Lane1Start, Lane1End, Lane2Start, Lane2End, IntersectionPoint, Tolerance))
            {
                // Verificar se a intersecção já existe (evitar duplicatas)
                bool bAlreadyExists = false;
                for (const FVector& ExistingPoint : OutIntersections)
                {
                    if (FVector::Dist(ExistingPoint, IntersectionPoint) < Tolerance)
                    {
                        bAlreadyExists = true;
                        break;
                    }
                }

                if (!bAlreadyExists)
                {
                    OutIntersections.Add(IntersectionPoint);
                }
            }
        }
    }
}

bool AAURACRONPCGLaneSystem::CalculateLineIntersection2D(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, FVector& OutIntersection, float Tolerance)
{
    // Calcular intersecção entre duas linhas em 2D (ignorando Z)
    float X1 = Line1Start.X, Y1 = Line1Start.Y;
    float X2 = Line1End.X, Y2 = Line1End.Y;
    float X3 = Line2Start.X, Y3 = Line2Start.Y;
    float X4 = Line2End.X, Y4 = Line2End.Y;

    float Denominator = (X1 - X2) * (Y3 - Y4) - (Y1 - Y2) * (X3 - X4);

    // Linhas paralelas
    if (FMath::Abs(Denominator) < KINDA_SMALL_NUMBER)
    {
        return false;
    }

    float T = ((X1 - X3) * (Y3 - Y4) - (Y1 - Y3) * (X3 - X4)) / Denominator;
    float U = -((X1 - X2) * (Y1 - Y3) - (Y1 - Y2) * (X1 - X3)) / Denominator;

    // Verificar se a intersecção está dentro dos segmentos
    if (T >= 0.0f && T <= 1.0f && U >= 0.0f && U <= 1.0f)
    {
        OutIntersection.X = X1 + T * (X2 - X1);
        OutIntersection.Y = Y1 + T * (Y2 - Y1);
        OutIntersection.Z = FMath::Lerp(Line1Start.Z, Line1End.Z, T); // Interpolar altura

        return true;
    }

    return false;
}

void AAURACRONPCGLaneSystem::GenerateIntersectionElements(const TArray<FVector>& IntersectionPoints, EAURACRONDeviceType DeviceType, int32 MaxElements)
{
    // Gerar elementos visuais nas intersecções baseado na capacidade do dispositivo
    int32 ElementsToGenerate = FMath::Min(IntersectionPoints.Num(), MaxElements);

    for (int32 i = 0; i < ElementsToGenerate; ++i)
    {
        FVector IntersectionPoint = IntersectionPoints[i];

        // Criar elemento de intersecção
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("LaneIntersection_%d_%d"), (int32)CurrentEnvironment, i));
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        if (AActor* IntersectionActor = GetWorld()->SpawnActor<AActor>(SpawnParams))
        {
            // Configurar componente visual baseado no tipo de dispositivo
            if (UStaticMeshComponent* IntersectionMesh = IntersectionActor->CreateDefaultSubobject<UStaticMeshComponent>(TEXT("IntersectionMesh")))
            {
                IntersectionActor->SetRootComponent(IntersectionMesh);
                IntersectionActor->SetActorLocation(IntersectionPoint);

                // Aplicar configurações baseadas no dispositivo
                ApplyIntersectionVisualsForDevice(IntersectionMesh, DeviceType);

                // Adicionar ao array de atores de interseção gerados
                FAURACRONActorArray& GeneratedActors = GeneratedIntersectionActorsByEnvironment.FindOrAdd(CurrentEnvironment);
                GeneratedActors.Actors.Add(IntersectionActor);
            }
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "GenerateIntersectionElements - Generated {0} intersection elements for device type {1} using modern UE 5.6 APIs",
           ElementsToGenerate, static_cast<int32>(DeviceType));
}

void AAURACRONPCGLaneSystem::ApplyIntersectionVisualsForDevice(UStaticMeshComponent* MeshComponent, EAURACRONDeviceType DeviceType)
{
    if (!MeshComponent)
    {
        return;
    }

    // Configurar visuais baseados no tipo de dispositivo
    switch (DeviceType)
    {
        case EAURACRONDeviceType::Entry:
        {
            // Visuais simples para Entry devices
            MeshComponent->SetRelativeScale3D(FVector(0.5f, 0.5f, 0.1f));
            
            if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
            {
                DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), FLinearColor(0.8f, 0.8f, 0.2f, 0.7f));
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Metallic")), 0.1f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Roughness")), 0.8f);
            }
            break;
        }

        case EAURACRONDeviceType::Mid:
        {
            // Visuais balanceados para Mid devices
            MeshComponent->SetRelativeScale3D(FVector(0.8f, 0.8f, 0.15f));
            
            if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
            {
                DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), FLinearColor(0.2f, 0.8f, 0.8f, 0.8f));
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Metallic")), 0.3f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Roughness")), 0.5f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("EmissiveIntensity")), 0.5f);
            }
            break;
        }

        case EAURACRONDeviceType::High:
        {
            // Visuais complexos para High devices
            MeshComponent->SetRelativeScale3D(FVector(1.2f, 1.2f, 0.2f));
            
            if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
            {
                DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), FLinearColor(0.8f, 0.2f, 0.8f, 0.9f));
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Metallic")), 0.7f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("Roughness")), 0.2f);
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("EmissiveIntensity")), 1.2f);
                
                // Adicionar efeitos de pulsação para High devices
                float PulseValue = FMath::Sin(GetWorld()->GetTimeSeconds() * 2.0f) * 0.5f + 0.5f;
                DynamicMaterial->SetScalarParameterValue(FName(TEXT("PulseIntensity")), PulseValue);
            }
            break;
        }

        default:
            // Configuração padrão
            MeshComponent->SetRelativeScale3D(FVector(0.8f, 0.8f, 0.15f));
            break;
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES AUXILIARES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGLaneSystem::ApplyEnvironmentModificationsToPoints(TArray<FVector>& Points, EAURACRONEnvironmentType EnvironmentType) const
{
    // Aplicar modificações ambientais aos pontos usando APIs modernas do UE 5.6
    if (Points.Num() == 0)
    {
        return;
    }

    // Aplicar modificações baseadas no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            // Planícies radiantes: suavizar pontos e adicionar brilho
            for (FVector& Point : Points)
            {
                Point.Z += FMath::Sin(Point.X * 0.01f) * 50.0f; // Ondulação suave
                Point.Z += FMath::Cos(Point.Y * 0.01f) * 30.0f; // Variação adicional
            }
            break;
        }

        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            // Firmamento Zephyr: elevar pontos e adicionar movimento aéreo
            for (FVector& Point : Points)
            {
                Point.Z += 200.0f; // Elevar 2 metros
                Point.Z += FMath::Sin(Point.X * 0.005f + Point.Y * 0.005f) * 100.0f; // Movimento flutuante
            }
            break;
        }

        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            // Reino do Purgatório: adicionar distorção e irregularidade
            for (FVector& Point : Points)
            {
                Point.X += FMath::RandRange(-100.0f, 100.0f); // Distorção aleatória
                Point.Y += FMath::RandRange(-100.0f, 100.0f);
                Point.Z += FMath::Sin(Point.X * 0.02f) * FMath::Cos(Point.Y * 0.02f) * 150.0f; // Distorção complexa
            }
            break;
        }

        default:
        {
            // Ambiente padrão: aplicar modificações mínimas
            for (FVector& Point : Points)
            {
                Point.Z += FMath::RandRange(-25.0f, 25.0f); // Variação pequena
            }
            break;
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "ApplyEnvironmentModificationsToPoints - Applied modifications to {0} points for environment {1} using modern UE 5.6 APIs",
           Points.Num(), static_cast<int32>(EnvironmentType));
}

EAURACRONJungleCampType AAURACRONPCGLaneSystem::DetermineCampType(const FVector& Position, EAURACRONEnvironmentType EnvironmentType)
{
    // Determinar tipo de camp baseado na posição e ambiente usando algoritmos modernos
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float DistanceFromCenter = FVector::Dist2D(Position, MapCenter);

    // Usar algoritmo baseado em distância e ambiente
    if (DistanceFromCenter < 1000.0f) // Dentro de 10 metros do centro
    {
        return EAURACRONJungleCampType::RadiantEssence; // Essência radiante no centro
    }
    else if (DistanceFromCenter < 2000.0f) // Entre 10 e 20 metros
    {
        // Camps de buff baseados no ambiente
        switch (EnvironmentType)
        {
            case EAURACRONEnvironmentType::RadiantPlains:
                return EAURACRONJungleCampType::RadiantEssence;
            case EAURACRONEnvironmentType::ZephyrFirmament:
                return EAURACRONJungleCampType::ChaosEssence;
            case EAURACRONEnvironmentType::PurgatoryRealm:
                return EAURACRONJungleCampType::SpectralPack;
            default:
                return EAURACRONJungleCampType::RadiantEssence;
        }
    }
    else if (DistanceFromCenter < 3000.0f) // Entre 20 e 30 metros
    {
        return EAURACRONJungleCampType::StoneGuardians;
    }
    else if (DistanceFromCenter < 4000.0f) // Entre 30 e 40 metros
    {
        return EAURACRONJungleCampType::PrismalToad;
    }
    else
    {
        return EAURACRONJungleCampType::WindSpirits;
    }
}

void AAURACRONPCGLaneSystem::ApplyCampMeshAndMaterial(UStaticMeshComponent* MeshComponent, EAURACRONJungleCampType CampType, EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar mesh e material ao camp usando APIs modernas do UE 5.6
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        return;
    }

    // Configurar propriedades básicas usando APIs modernas
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionObjectType(ECC_WorldStatic);
    MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);

    // Aplicar escala baseada no tipo de camp
    FVector CampScale = FVector(1.0f);
    switch (CampType)
    {
        case EAURACRONJungleCampType::WindSpirits:
            CampScale = FVector(0.8f);
            break;
        case EAURACRONJungleCampType::PrismalToad:
            CampScale = FVector(1.0f);
            break;
        case EAURACRONJungleCampType::StoneGuardians:
            CampScale = FVector(1.3f);
            break;
        case EAURACRONJungleCampType::SpectralPack:
            CampScale = FVector(1.5f);
            break;
        case EAURACRONJungleCampType::RadiantEssence:
        case EAURACRONJungleCampType::ChaosEssence:
        case EAURACRONJungleCampType::FluxCrawler:
            CampScale = FVector(2.0f);
            break;
    }

    MeshComponent->SetWorldScale3D(CampScale);

    // Criar material dinâmico baseado no ambiente
    if (UMaterialInterface* BaseMaterial = MeshComponent->GetMaterial(0))
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
        {
            // Aplicar cores baseadas no ambiente
            FLinearColor EnvironmentColor;
            switch (EnvironmentType)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    EnvironmentColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado radiante
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    EnvironmentColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul aéreo
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    EnvironmentColor = FLinearColor(0.9f, 0.3f, 0.3f, 1.0f); // Vermelho sombrio
                    break;
                default:
                    EnvironmentColor = FLinearColor::White;
                    break;
            }

            DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), EnvironmentColor);
            DynamicMaterial->SetScalarParameterValue(FName(TEXT("CampIntensity")), static_cast<float>(CampType) * 0.2f + 0.5f);
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "ApplyCampMeshAndMaterial - Applied mesh and material for camp type {0} in environment {1} using modern UE 5.6 APIs",
           static_cast<int32>(CampType), static_cast<int32>(EnvironmentType));
}

void AAURACRONPCGLaneSystem::ApplyStructureMeshAndMaterial(UStaticMeshComponent* MeshComponent, EAURACRONDefensiveStructure StructureType, EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar mesh e material à estrutura usando APIs modernas do UE 5.6
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        return;
    }

    // Configurar propriedades físicas usando APIs modernas
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionObjectType(ECC_WorldStatic);
    MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);
    MeshComponent->SetCanEverAffectNavigation(true);

    // Aplicar escala baseada no tipo de estrutura
    FVector StructureScale = FVector(1.0f);
    switch (StructureType)
    {
        case EAURACRONDefensiveStructure::OuterTower:
            StructureScale = FVector(1.0f, 1.0f, 1.5f); // Torres externas mais altas
            break;
        case EAURACRONDefensiveStructure::InnerTower:
            StructureScale = FVector(1.2f, 1.2f, 2.0f); // Torres internas maiores
            break;
        case EAURACRONDefensiveStructure::InhibitorTower:
            StructureScale = FVector(1.5f, 1.5f, 2.5f); // Torres inibidoras muito grandes
            break;
        case EAURACRONDefensiveStructure::Inhibitor:
            StructureScale = FVector(2.0f, 2.0f, 1.0f); // Inibidores largos e baixos
            break;
        case EAURACRONDefensiveStructure::Nexus:
            StructureScale = FVector(3.0f, 3.0f, 3.0f); // Nexus massivo
            break;
    }

    MeshComponent->SetWorldScale3D(StructureScale);

    // Criar material dinâmico baseado no ambiente e estrutura
    if (UMaterialInterface* BaseMaterial = MeshComponent->GetMaterial(0))
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0))
        {
            // Aplicar cores baseadas no ambiente
            FLinearColor StructureColor;
            float StructureIntensity = 1.0f;

            switch (EnvironmentType)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    StructureColor = FLinearColor(1.0f, 0.8f, 0.4f, 1.0f); // Dourado brilhante
                    StructureIntensity = 1.2f;
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    StructureColor = FLinearColor(0.6f, 0.8f, 1.0f, 1.0f); // Azul cristalino
                    StructureIntensity = 1.0f;
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    StructureColor = FLinearColor(0.8f, 0.2f, 0.2f, 1.0f); // Vermelho sombrio
                    StructureIntensity = 0.8f;
                    break;
                default:
                    StructureColor = FLinearColor(0.7f, 0.7f, 0.7f, 1.0f); // Cinza neutro
                    StructureIntensity = 0.9f;
                    break;
            }

            // Aplicar intensidade baseada no tipo de estrutura
            float TypeMultiplier = 1.0f;
            switch (StructureType)
            {
                case EAURACRONDefensiveStructure::OuterTower:
                    TypeMultiplier = 0.8f;
                    break;
                case EAURACRONDefensiveStructure::InnerTower:
                    TypeMultiplier = 1.0f;
                    break;
                case EAURACRONDefensiveStructure::InhibitorTower:
                    TypeMultiplier = 1.3f;
                    break;
                case EAURACRONDefensiveStructure::Inhibitor:
                    TypeMultiplier = 1.5f;
                    break;
                case EAURACRONDefensiveStructure::Nexus:
                    TypeMultiplier = 2.0f;
                    break;
            }

            DynamicMaterial->SetVectorParameterValue(FName(TEXT("BaseColor")), StructureColor);
            DynamicMaterial->SetScalarParameterValue(FName(TEXT("StructureIntensity")), StructureIntensity * TypeMultiplier);
            DynamicMaterial->SetScalarParameterValue(FName(TEXT("DefensivePower")), static_cast<float>(StructureType) * 0.25f + 0.5f);
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "ApplyStructureMeshAndMaterial - Applied mesh and material for structure type {0} in environment {1} using modern UE 5.6 APIs",
           static_cast<int32>(StructureType), static_cast<int32>(EnvironmentType));
}

void AAURACRONPCGLaneSystem::ApplyEnvironmentEffectsToStructure(AActor* StructureActor, EAURACRONEnvironmentType EnvironmentType)
{
    // Aplicar efeitos ambientais à estrutura usando APIs modernas do UE 5.6
    if (!StructureActor || !IsValid(StructureActor))
    {
        return;
    }

    // Aplicar efeitos baseados no ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            // Efeito de brilho radiante
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("RadiantGlow")), 1.5f);
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("RadiantColor")), FLinearColor(1.0f, 0.9f, 0.7f));
                }
            }
            break;
        }

        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            // Efeito de flutuação aérea
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("AerialFloat")), 1.2f);
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("ZephyrWind")), FLinearColor(0.7f, 0.9f, 1.0f));
                }
            }
            break;
        }

        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            // Efeito de distorção sombria
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("ShadowDistortion")), 1.8f);
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("PurgatoryAura")), FLinearColor(0.9f, 0.3f, 0.3f));
                }
            }
            break;
        }

        default:
            // Efeitos padrão mínimos
            if (UStaticMeshComponent* MeshComp = StructureActor->FindComponentByClass<UStaticMeshComponent>())
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("DefaultEffect")), 1.0f);
                }
            }
            break;
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "ApplyEnvironmentEffectsToStructure - Applied environment effects for environment {0} to structure {1} using modern UE 5.6 APIs",
           static_cast<int32>(EnvironmentType), StructureActor->GetName());
}

FVector AAURACRONPCGLaneSystem::GetEnvironmentTowerOffset(EAURACRONEnvironmentType EnvironmentType)
{
    // Obter offset de torre baseado no ambiente usando algoritmos modernos
    FVector Offset = FVector::ZeroVector;

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Torres nas planícies radiantes ficam ligeiramente elevadas
            Offset = FVector(0.0f, 0.0f, 50.0f);
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Torres no firmamento ficam flutuando mais alto
            Offset = FVector(0.0f, 0.0f, 150.0f);
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Torres no purgatório ficam parcialmente enterradas
            Offset = FVector(0.0f, 0.0f, -25.0f);
            break;

        default:
            // Offset padrão
            Offset = FVector(0.0f, 0.0f, 25.0f);
            break;
    }

    return Offset;
}

void AAURACRONPCGLaneSystem::ApplyPhaseEffectsToActor(AActor* Actor, float PhaseIntensity, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos de fase ao ator usando APIs modernas do UE 5.6
    if (!Actor || !IsValid(Actor))
    {
        return;
    }

    // Aplicar efeitos a todos os componentes de mesh do ator
    TArray<UStaticMeshComponent*> MeshComponents;
    Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

    for (UStaticMeshComponent* MeshComp : MeshComponents)
    {
        if (MeshComp && IsValid(MeshComp))
        {
            ApplyPhaseEffectsToComponent(MeshComp, PhaseIntensity, PhaseColor);
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "ApplyPhaseEffectsToActor - Applied phase effects to actor {0} with intensity {1} using modern UE 5.6 APIs",
           Actor->GetName(), PhaseIntensity);
}

void AAURACRONPCGLaneSystem::ApplyPhaseEffectsToComponent(UActorComponent* Component, float PhaseIntensity, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos de fase ao componente usando APIs modernas do UE 5.6
    if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
    {
        if (MeshComp && IsValid(MeshComp))
        {
            // Criar material dinâmico se necessário
            if (UMaterialInterface* BaseMaterial = MeshComp->GetMaterial(0))
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    // Aplicar parâmetros de fase
                    DynamicMaterial->SetVectorParameterValue(FName(TEXT("PhaseColor")), PhaseColor);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("PhaseIntensity")), PhaseIntensity);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("PhaseBlend")), PhaseIntensity * 0.8f);
                }
            }

            // Aplicar visibilidade baseada na intensidade da fase
            bool bShouldBeVisible = PhaseIntensity > 0.1f;
            MeshComp->SetVisibility(bShouldBeVisible);
        }
    }
}

FAURACRONEnvironmentConfig AAURACRONPCGLaneSystem::GetEnvironmentConfiguration(EAURACRONEnvironmentType EnvironmentType)
{
    // Obter configuração do ambiente usando dados modernos do UE 5.6
    FAURACRONEnvironmentConfig Config;

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            Config.PrimaryColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado radiante
            Config.LightIntensity = 1.3f;
            Config.MaterialRoughness = 0.3f;
            Config.ActivityScale = 1.2f;
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            Config.PrimaryColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul aéreo
            Config.LightIntensity = 1.1f;
            Config.MaterialRoughness = 0.2f;
            Config.ActivityScale = 1.0f;
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            Config.PrimaryColor = FLinearColor(0.9f, 0.3f, 0.3f, 1.0f); // Vermelho sombrio
            Config.LightIntensity = 0.8f;
            Config.MaterialRoughness = 0.7f;
            Config.ActivityScale = 1.5f;
            break;

        default:
            Config.PrimaryColor = FLinearColor::White;
            Config.LightIntensity = 1.0f;
            Config.MaterialRoughness = 0.5f;
            Config.ActivityScale = 1.0f;
            break;
    }

    return Config;
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGLaneSystem::ClearGeneratedElementsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Limpar elementos gerados para ambiente específico usando APIs modernas do UE 5.6
    int32 ClearedCount = 0;

    // Limpar componentes gerados para o ambiente
    if (FAURACRONComponentArray* ComponentArray = GeneratedComponentsByEnvironment.Find(Environment))
    {
        for (UObject* Component : ComponentArray->Components)
        {
            if (AActor* Actor = Cast<AActor>(Component))
            {
                if (IsValid(Actor))
                {
                    Actor->Destroy();
                    ClearedCount++;
                }
            }
            else if (UActorComponent* ActorComponent = Cast<UActorComponent>(Component))
            {
                if (IsValid(ActorComponent))
                {
                    ActorComponent->DestroyComponent();
                    ClearedCount++;
                }
            }
        }
        ComponentArray->Components.Empty();
    }

    // Limpar estruturas defensivas para o ambiente
    if (FAURACRONActorArray* ActorArray = DefensiveStructuresByEnvironment.Find(Environment))
    {
        for (AActor* Actor : ActorArray->Actors)
        {
            if (IsValid(Actor))
            {
                Actor->Destroy();
                ClearedCount++;
            }
        }
        ActorArray->Actors.Empty();
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "ClearGeneratedElementsForEnvironment - Cleared {0} elements for environment {1} using modern UE 5.6 APIs",
           ClearedCount, static_cast<int32>(Environment));
}

bool AAURACRONPCGLaneSystem::IsValidJungleCampPosition(const FVector& Position, float MinDistanceFromLanes)
{
    // Validar posição para camp da jungle usando algoritmos modernos do UE 5.6
    if (!IsValid(GetWorld()))
    {
        return false;
    }

    // Verificar distância mínima de todas as lanes no ambiente atual
    for (const auto& LanePair : LaneInfos)
    {
        EAURACRONLaneType LaneType = LanePair.Key;
        const FAURACRONLaneInfo& LaneInfo = LanePair.Value;

        // Obter pontos da lane para o ambiente atual
        const FAURACRONVectorArray* LanePointsArray = LaneInfo.LanePointsByEnvironment.Find(CurrentEnvironment);
        if (LanePointsArray)
        {
            // Verificar distância mínima para todos os pontos da lane
            for (const FVector& LanePoint : LanePointsArray->Vectors)
            {
                float Distance = FVector::Dist2D(Position, LanePoint);
                if (Distance < MinDistanceFromLanes)
                {
                    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "IsValidJungleCampPosition - Position too close to lane {0} (distance: {1}, min: {2})",
                           static_cast<int32>(LaneType), Distance, MinDistanceFromLanes);
                    return false;
                }
            }
        }
    }

    // Verificar colisão com terreno usando trace moderno do UE 5.6
    FHitResult HitResult;
    FVector TraceStart = Position + FVector(0.0f, 0.0f, 1000.0f);
    FVector TraceEnd = Position - FVector(0.0f, 0.0f, 1000.0f);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        TraceStart,
        TraceEnd,
        ECC_WorldStatic,
        QueryParams
    );

    if (!bHit)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "IsValidJungleCampPosition - No ground found at position");
        return false;
    }

    // Verificar se a inclinação do terreno é aceitável
    float SlopeAngle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(HitResult.Normal, FVector::UpVector)));
    if (SlopeAngle > 30.0f) // Máximo 30 graus de inclinação
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "IsValidJungleCampPosition - Terrain too steep (angle: {0} degrees)", SlopeAngle);
        return false;
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "IsValidJungleCampPosition - Position is valid for jungle camp");
    return true;
}

FAURACRONJungleCamp AAURACRONPCGLaneSystem::GetJungleCampConfig(const FString& CampType, EAURACRONEnvironmentType Environment)
{
    // Obter configuração de camp baseada no tipo e ambiente usando dados modernos do UE 5.6
    FAURACRONJungleCamp CampConfig;

    // Configurar propriedades básicas
    CampConfig.MonsterType = CampType;
    CampConfig.bIsBuffCamp = false;
    CampConfig.DifficultyLevel = 1;
    CampConfig.RespawnTime = 120.0f; // 2 minutos padrão

    // Configurar baseado no tipo de camp
    if (CampType.Contains(TEXT("Guardian")))
    {
        CampConfig.bIsBuffCamp = true;
        CampConfig.DifficultyLevel = 3;
        CampConfig.RespawnTime = 300.0f; // 5 minutos para buffs
        CampConfig.Radius = 800.0f;
    }
    else if (CampType.Contains(TEXT("Golem")) || CampType.Contains(TEXT("Elemental")))
    {
        CampConfig.DifficultyLevel = 2;
        CampConfig.RespawnTime = 180.0f; // 3 minutos
        CampConfig.Radius = 600.0f;
    }
    else if (CampType.Contains(TEXT("Spirit")) || CampType.Contains(TEXT("Wisp")))
    {
        CampConfig.DifficultyLevel = 1;
        CampConfig.RespawnTime = 90.0f; // 1.5 minutos
        CampConfig.Radius = 400.0f;
    }
    else
    {
        // Configuração padrão
        CampConfig.DifficultyLevel = 1;
        CampConfig.RespawnTime = 120.0f;
        CampConfig.Radius = 500.0f;
    }

    // Aplicar modificações baseadas no ambiente
    switch (Environment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            CampConfig.RespawnTime *= 0.9f; // 10% mais rápido
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            CampConfig.RespawnTime *= 1.0f; // Tempo normal
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            CampConfig.RespawnTime *= 1.2f; // 20% mais lento
            CampConfig.DifficultyLevel = FMath::Min(5, CampConfig.DifficultyLevel + 1); // Mais difícil
            break;

        default:
            break;
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "GetJungleCampConfig - Generated config for camp type {0} in environment {1}",
           CampType, static_cast<int32>(Environment));

    return CampConfig;
}

void AAURACRONPCGLaneSystem::UpdateVisibilityForPhase(EAURACRONMapPhase Phase)
{
    // Atualizar visibilidade baseada na fase usando APIs modernas do UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Obter configurações de visibilidade baseadas na fase
    float VisibilityMultiplier = 1.0f;
    float FogDensity = 0.0f;
    FLinearColor PhaseColor = FLinearColor::White;

    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            VisibilityMultiplier = 1.2f; // Maior visibilidade no início
            FogDensity = 0.1f;
            PhaseColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado suave
            break;

        case EAURACRONMapPhase::Convergence:
            VisibilityMultiplier = 1.0f; // Visibilidade normal
            FogDensity = 0.2f;
            PhaseColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f); // Branco puro
            break;

        case EAURACRONMapPhase::Intensification:
            VisibilityMultiplier = 0.8f; // Visibilidade reduzida
            FogDensity = 0.4f;
            PhaseColor = FLinearColor(1.0f, 0.6f, 0.4f, 1.0f); // Laranja intenso
            break;

        case EAURACRONMapPhase::Resolution:
            VisibilityMultiplier = 0.6f; // Visibilidade muito reduzida
            FogDensity = 0.6f;
            PhaseColor = FLinearColor(0.4f, 0.6f, 1.0f, 1.0f); // Azul noturno
            break;

        default:
            VisibilityMultiplier = 1.0f;
            FogDensity = 0.2f;
            PhaseColor = FLinearColor::White;
            break;
    }

    // Aplicar efeitos de visibilidade a todos os componentes gerados
    for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvironmentPair.Key;
        FAURACRONComponentArray& ComponentArray = EnvironmentPair.Value;

        // Só aplicar ao ambiente atual
        bool bIsCurrentEnvironment = (Environment == CurrentEnvironment);
        float EnvironmentVisibility = bIsCurrentEnvironment ? VisibilityMultiplier : 0.0f;

        for (UObject* Component : ComponentArray.Components)
        {
            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Component))
            {
                if (IsValid(PrimComp))
                {
                    // Aplicar visibilidade baseada na fase
                    PrimComp->SetVisibility(EnvironmentVisibility > 0.1f);

                    // Aplicar efeitos de material se for um componente de mesh
                    if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(PrimComp))
                    {
                        ApplyPhaseEffectsToComponent(MeshComp, EnvironmentVisibility, PhaseColor);
                    }
                }
            }
        }
    }

    // Aplicar efeitos às estruturas defensivas
    for (auto& EnvironmentPair : DefensiveStructuresByEnvironment)
    {
        EAURACRONEnvironmentType Environment = EnvironmentPair.Key;
        FAURACRONActorArray& ActorArray = EnvironmentPair.Value;

        bool bIsCurrentEnvironment = (Environment == CurrentEnvironment);
        float EnvironmentVisibility = bIsCurrentEnvironment ? VisibilityMultiplier : 0.0f;

        for (AActor* Actor : ActorArray.Actors)
        {
            if (IsValid(Actor))
            {
                // Aplicar visibilidade ao ator
                Actor->SetActorHiddenInGame(EnvironmentVisibility <= 0.1f);

                // Aplicar efeitos de fase ao ator
                ApplyPhaseEffectsToActor(Actor, EnvironmentVisibility, PhaseColor);
            }
        }
    }

    // Atualizar fase atual
    CurrentMapPhase = Phase;

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "UpdateVisibilityForPhase - Updated visibility for phase {0} with multiplier {1} and fog density {2}",
           static_cast<int32>(Phase), VisibilityMultiplier, FogDensity);
}

void AAURACRONPCGLaneSystem::ApplyPhaseEffectsToComponent(UStaticMeshComponent* MeshComponent, float VisibilityMultiplier, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos de fase ao componente usando APIs modernas do UE 5.6
    if (!IsValid(MeshComponent))
    {
        return;
    }

    // Criar material dinâmico se necessário
    UMaterialInstanceDynamic* DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(0);
    if (IsValid(DynamicMaterial))
    {
        // Aplicar cor da fase
        DynamicMaterial->SetVectorParameterValue(TEXT("PhaseColor"), PhaseColor);

        // Aplicar multiplicador de visibilidade como opacidade
        float Opacity = FMath::Clamp(VisibilityMultiplier, 0.0f, 1.0f);
        DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), Opacity);

        // Aplicar efeito de brilho baseado na fase
        float EmissiveStrength = FMath::Lerp(0.0f, 2.0f, 1.0f - VisibilityMultiplier);
        DynamicMaterial->SetScalarParameterValue(TEXT("EmissiveStrength"), EmissiveStrength);

        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "ApplyPhaseEffectsToComponent - Applied phase effects with opacity {0} and emissive {1}",
               Opacity, EmissiveStrength);
    }
}



void AAURACRONPCGLaneSystem::ApplyDefensiveStructurePhaseEffects(AActor* StructureActor, float VisibilityMultiplier, const FLinearColor& PhaseColor)
{
    // Aplicar efeitos especiais de fase para estruturas defensivas usando APIs modernas do UE 5.6
    if (!IsValid(StructureActor))
    {
        return;
    }

    // Obter componentes de partículas se existirem
    TArray<UParticleSystemComponent*> ParticleComponents;
    StructureActor->GetComponents<UParticleSystemComponent>(ParticleComponents);

    for (UParticleSystemComponent* ParticleComp : ParticleComponents)
    {
        if (IsValid(ParticleComp))
        {
            // Ajustar intensidade das partículas baseada na visibilidade
            float ParticleIntensity = FMath::Lerp(0.2f, 1.0f, VisibilityMultiplier);
            ParticleComp->SetFloatParameter(TEXT("Intensity"), ParticleIntensity);

            // Aplicar cor da fase às partículas
            ParticleComp->SetColorParameter(TEXT("Color"), PhaseColor);
        }
    }

    // Obter componentes de luz se existirem
    TArray<ULightComponent*> LightComponents;
    StructureActor->GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (IsValid(LightComp))
        {
            // Ajustar intensidade da luz baseada na visibilidade
            float LightIntensity = FMath::Lerp(0.1f, 1.0f, VisibilityMultiplier);
            LightComp->SetIntensity(LightIntensity * 1000.0f); // Multiplicar por 1000 para unidades do UE

            // Aplicar cor da fase à luz
            LightComp->SetLightColor(PhaseColor);
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "ApplyDefensiveStructurePhaseEffects - Applied special effects to structure {0}",
           StructureActor->GetName());
}

void AAURACRONPCGLaneSystem::OnMapContraction(float ContractionFactor)
{
    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "OnMapContraction - Aplicando contração {0} ao sistema de lanes", ContractionFactor);

    // Aplicar contração às splines das lanes
    FVector MapCenter = FVector::ZeroVector; // Centro do mapa

    for (auto& LanePair : LaneSplines)
    {
        USplineComponent* LaneSpline = LanePair.Value;
        if (IsValid(LaneSpline))
        {
            // Contrair cada ponto da spline em direção ao centro
            int32 NumPoints = LaneSpline->GetNumberOfSplinePoints();
            for (int32 i = 0; i < NumPoints; ++i)
            {
                FVector OriginalLocation = LaneSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
                FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
                FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

                LaneSpline->SetLocationAtSplinePoint(i, NewLocation, ESplineCoordinateSpace::World);
            }

            // Atualizar spline
            LaneSpline->UpdateSpline();
        }
    }

    // Aplicar contração aos componentes de mesh das lanes
    for (UStaticMeshComponent* MeshComp : LaneMeshComponents)
    {
        if (IsValid(MeshComp))
        {
            FVector OriginalLocation = MeshComp->GetComponentLocation();
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            MeshComp->SetWorldLocation(NewLocation);
        }
    }

    // Aplicar contração aos camps da jungle
    for (FAURACRONJungleCamp& Camp : JungleCamps)
    {
        FVector OriginalLocation = Camp.Position;
        FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
        Camp.Position = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "OnMapContraction - Contração aplicada com sucesso usando APIs modernas UE 5.6");
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES ADICIONADAS - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGLaneSystem::PerformanceUpdate()
{
    // Sistema de update de performance otimizado usando Timer moderno UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Atualizar efeitos baseados na fase do mapa
    if (HasAuthority())
    {
        // Verificar se a fase mudou usando subsistema moderno
        if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase NewPhase = PCGSubsystem->GetCurrentMapPhase();
            if (NewPhase != CurrentMapPhase)
            {
                UpdateForMapPhase(NewPhase);
            }
        }

        // Otimizar partículas baseado na performance
        OptimizeParticleEffects();

        // Atualizar streaming de assets
        UpdateAssetStreaming();
    }
}

void AAURACRONPCGLaneSystem::InitializeAsyncStreaming()
{
    // Inicializar sistema de streaming assíncrono moderno UE 5.6
    if (!StreamableManager)
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Error, "InitializeAsyncStreaming: StreamableManager not available");
        return;
    }

    // Configurar assets para pré-carregamento
    TArray<FSoftObjectPath> AssetsToPreload;

    // Assets de ambiente
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Environments/RadiantPlains/Materials/M_RadiantPlains_Base")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Environments/ZephyrFirmament/Materials/M_ZephyrFirmament_Base")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Environments/PurgatoryRealm/Materials/M_PurgatoryRealm_Base")));

    // Assets de lanes
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Lanes/Meshes/SM_Lane_Segment")));
    AssetsToPreload.Add(FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Lanes/Materials/M_Lane_Base")));

    // Iniciar carregamento assíncrono
    if (AssetsToPreload.Num() > 0)
    {
        StreamableHandle = StreamableManager->RequestAsyncLoad(
            AssetsToPreload,
            FStreamableDelegate::CreateUObject(this, &AAURACRONPCGLaneSystem::OnAssetsLoaded),
            FStreamableManager::AsyncLoadHighPriority
        );

        UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "InitializeAsyncStreaming: Started loading {0} assets asynchronously", AssetsToPreload.Num());
    }
}

void AAURACRONPCGLaneSystem::OnAssetsLoaded()
{
    // Callback quando assets são carregados assincronamente
    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "OnAssetsLoaded: Assets loaded successfully using modern UE 5.6 streaming");

    // Aplicar assets carregados aos componentes
    ApplyLoadedAssets();
}

void AAURACRONPCGLaneSystem::ApplyLoadedAssets()
{
    // Aplicar assets carregados aos componentes usando APIs modernas UE 5.6
    if (!StreamableHandle.IsValid() || !StreamableHandle->HasLoadCompleted())
    {
        UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "ApplyLoadedAssets: Assets not fully loaded yet");
        return;
    }

    // Aplicar materiais carregados aos componentes de lane
    for (UStaticMeshComponent* MeshComp : LaneMeshComponents)
    {
        if (IsValid(MeshComp))
        {
            // Aplicar material baseado no ambiente atual
            ApplyEnvironmentMaterial(MeshComp, CurrentEnvironment);
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "ApplyLoadedAssets: Applied loaded assets to {0} lane mesh components", LaneMeshComponents.Num());
}

void AAURACRONPCGLaneSystem::ApplyEnvironmentMaterial(UStaticMeshComponent* MeshComponent, EAURACRONEnvironmentType Environment)
{
    // Aplicar material específico do ambiente usando APIs modernas UE 5.6
    if (!IsValid(MeshComponent))
    {
        return;
    }

    FString MaterialPath;
    switch (Environment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            MaterialPath = TEXT("/Game/AURACRON/Environments/RadiantPlains/Materials/M_RadiantPlains_Base");
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            MaterialPath = TEXT("/Game/AURACRON/Environments/ZephyrFirmament/Materials/M_ZephyrFirmament_Base");
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            MaterialPath = TEXT("/Game/AURACRON/Environments/PurgatoryRealm/Materials/M_PurgatoryRealm_Base");
            break;

        default:
            MaterialPath = TEXT("/Game/AURACRON/PCG/Lanes/Materials/M_Lane_Base");
            break;
    }

    // Carregar material usando StreamableManager
    if (StreamableManager)
    {
        FSoftObjectPath MaterialSoftPath(MaterialPath);
        if (UMaterialInterface* LoadedMaterial = StreamableManager->LoadSynchronous<UMaterialInterface>(MaterialSoftPath))
        {
            MeshComponent->SetMaterial(0, LoadedMaterial);
            UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "Applied environment material for environment {0}", static_cast<int32>(Environment));
        }
    }
}

void AAURACRONPCGLaneSystem::OptimizeParticleEffects()
{
    // Otimizar efeitos de partículas baseado na performance usando APIs modernas UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Obter informações de performance
    float CurrentFrameRate = 1.0f / GetWorld()->GetDeltaSeconds();
    bool bLowPerformance = CurrentFrameRate < 30.0f;

    // Ajustar orçamento de partículas baseado na performance
    int32 TargetParticleCount = bLowPerformance ? MaxParticlesPerEnvironment / 2 : MaxParticlesPerEnvironment;

    // Contar partículas atuais
    CurrentParticleCount = 0;

    // Otimizar componentes Niagara
    TArray<UNiagaraComponent*> NiagaraComponents;
    for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
    {
        if (EnvironmentPair.Key == CurrentEnvironment) // Só otimizar ambiente atual
        {
            for (UObject* Component : EnvironmentPair.Value.Components)
            {
                if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
                {
                    if (IsValid(NiagaraComp))
                    {
                        NiagaraComponents.Add(NiagaraComp);
                    }
                }
            }
        }
    }

    // Aplicar otimizações
    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (CurrentParticleCount >= TargetParticleCount)
        {
            // Desabilitar componentes excedentes
            NiagaraComp->SetActive(false);
        }
        else
        {
            // Habilitar e otimizar componentes necessários
            NiagaraComp->SetActive(true);

            // Ajustar qualidade baseada na performance
            float QualityMultiplier = bLowPerformance ? 0.5f : 1.0f;
            NiagaraComp->SetFloatParameter(TEXT("QualityMultiplier"), QualityMultiplier);

            CurrentParticleCount += 100; // Estimativa de partículas por componente
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "OptimizeParticleEffects: Optimized {0} Niagara components, target particles: {1}",
             NiagaraComponents.Num(), TargetParticleCount);
}

void AAURACRONPCGLaneSystem::UpdateAssetStreaming()
{
    // Atualizar streaming de assets baseado na proximidade usando APIs modernas UE 5.6
    if (!IsValid(GetWorld()) || !StreamableManager)
    {
        return;
    }

    // Obter posições dos jogadores para determinar proximidade
    TArray<FVector> PlayerPositions;
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        if (APlayerController* PC = Iterator->Get())
        {
            if (APawn* PlayerPawn = PC->GetPawn())
            {
                PlayerPositions.Add(PlayerPawn->GetActorLocation());
            }
        }
    }

    // Determinar quais assets devem ser carregados baseado na proximidade
    TArray<FSoftObjectPath> AssetsToLoad;
    TArray<FSoftObjectPath> AssetsToUnload;

    for (const FVector& PlayerPos : PlayerPositions)
    {
        float DistanceToPlayer = FVector::Dist(PlayerPos, GetActorLocation());

        if (DistanceToPlayer <= StreamingRadius)
        {
            // Carregar assets próximos
            AddNearbyAssetsToLoad(PlayerPos, AssetsToLoad);
        }
        else
        {
            // Descarregar assets distantes
            AddDistantAssetsToUnload(PlayerPos, AssetsToUnload);
        }
    }

    // Executar carregamento/descarregamento assíncrono
    if (AssetsToLoad.Num() > 0)
    {
        StreamableManager->RequestAsyncLoad(AssetsToLoad, FStreamableDelegate(), FStreamableManager::AsyncLoadHighPriority);
        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "UpdateAssetStreaming: Requested loading of {0} assets", AssetsToLoad.Num());
    }

    if (AssetsToUnload.Num() > 0)
    {
        for (const FSoftObjectPath& AssetPath : AssetsToUnload)
        {
            StreamableManager->Unload(AssetPath);
        }
        UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "UpdateAssetStreaming: Unloaded {0} assets", AssetsToUnload.Num());
    }
}

void AAURACRONPCGLaneSystem::AddNearbyAssetsToLoad(const FVector& PlayerPosition, TArray<FSoftObjectPath>& AssetsToLoad)
{
    // Adicionar assets próximos para carregamento usando APIs modernas UE 5.6
    float LoadDistance = StreamingRadius * 0.5f; // Carregar assets dentro de 50% do raio

    // Verificar proximidade a lanes
    for (const auto& LanePair : LaneInfos)
    {
        const FAURACRONLaneInfo& LaneInfo = LanePair.Value;
        const FAURACRONVectorArray* LanePoints = LaneInfo.LanePointsByEnvironment.Find(CurrentEnvironment);

        if (LanePoints)
        {
            for (const FVector& LanePoint : LanePoints->Vectors)
            {
                float DistanceToLane = FVector::Dist(PlayerPosition, LanePoint);
                if (DistanceToLane <= LoadDistance)
                {
                    // Adicionar assets específicos da lane
                    AssetsToLoad.AddUnique(FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Lanes/Meshes/SM_Lane_Segment")));
                    AssetsToLoad.AddUnique(FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Lanes/Materials/M_Lane_Base")));
                    break; // Só precisamos adicionar uma vez por lane
                }
            }
        }
    }

    // Verificar proximidade a camps da jungle
    for (const FAURACRONJungleCamp& Camp : JungleCamps)
    {
        float DistanceToCamp = FVector::Dist(PlayerPosition, Camp.Position);
        if (DistanceToCamp <= LoadDistance)
        {
            // Adicionar assets específicos do camp
            AssetsToLoad.AddUnique(FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Jungle/Meshes/SM_JungleCamp_Base")));
            AssetsToLoad.AddUnique(FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Jungle/Materials/M_JungleCamp_Base")));
        }
    }
}

void AAURACRONPCGLaneSystem::AddDistantAssetsToUnload(const FVector& PlayerPosition, TArray<FSoftObjectPath>& AssetsToUnload)
{
    // Adicionar assets distantes para descarregamento usando APIs modernas UE 5.6
    float UnloadDistance = StreamingRadius * 1.5f; // Descarregar assets além de 150% do raio

    // Assets que podem ser descarregados quando distantes
    TArray<FSoftObjectPath> PotentialUnloadAssets = {
        FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Lanes/Effects/NS_Lane_Particles")),
        FSoftObjectPath(TEXT("/Game/AURACRON/PCG/Jungle/Effects/NS_Jungle_Ambient")),
        FSoftObjectPath(TEXT("/Game/AURACRON/Environments/RadiantPlains/Effects/NS_RadiantPlains_Ambient")),
        FSoftObjectPath(TEXT("/Game/AURACRON/Environments/ZephyrFirmament/Effects/NS_ZephyrFirmament_Ambient")),
        FSoftObjectPath(TEXT("/Game/AURACRON/Environments/PurgatoryRealm/Effects/NS_PurgatoryRealm_Ambient"))
    };

    // Verificar se o jogador está longe o suficiente para descarregar
    float DistanceToSystem = FVector::Dist(PlayerPosition, GetActorLocation());
    if (DistanceToSystem >= UnloadDistance)
    {
        AssetsToUnload.Append(PotentialUnloadAssets);
    }
}

void AAURACRONPCGLaneSystem::ApplyTransitionEffects(float FadeAlpha)
{
    // Aplicar efeitos de transição suaves usando APIs modernas UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Aplicar fade aos componentes do ambiente atual
    const FAURACRONComponentArray* CurrentComponents = GeneratedComponentsByEnvironment.Find(CurrentEnvironment);
    if (CurrentComponents)
    {
        for (UObject* Component : CurrentComponents->Components)
        {
            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Component))
            {
                if (IsValid(PrimComp))
                {
                    // Aplicar fade usando material dinâmico
                    if (UMaterialInstanceDynamic* DynamicMaterial = PrimComp->CreateAndSetMaterialInstanceDynamic(0))
                    {
                        DynamicMaterial->SetScalarParameterValue(TEXT("TransitionAlpha"), FadeAlpha);
                    }
                }
            }
        }
    }

    // Aplicar fade aos componentes do ambiente de destino
    const FAURACRONComponentArray* TargetComponents = GeneratedComponentsByEnvironment.Find(TargetEnvironment);
    if (TargetComponents)
    {
        for (UObject* Component : TargetComponents->Components)
        {
            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Component))
            {
                if (IsValid(PrimComp))
                {
                    // Aplicar fade inverso para o ambiente de destino
                    if (UMaterialInstanceDynamic* DynamicMaterial = PrimComp->CreateAndSetMaterialInstanceDynamic(0))
                    {
                        DynamicMaterial->SetScalarParameterValue(TEXT("TransitionAlpha"), 1.0f - FadeAlpha);
                    }
                }
            }
        }
    }
}

void AAURACRONPCGLaneSystem::UpdateTransitionVisuals(float NormalizedProgress)
{
    // Atualizar visuais durante transição usando APIs modernas UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Aplicar efeitos de distorção baseados no progresso
    float DistortionStrength = FMath::Sin(NormalizedProgress * PI) * 0.5f; // Pico no meio da transição

    // Atualizar componentes Niagara com parâmetros de transição
    for (auto& EnvironmentPair : GeneratedComponentsByEnvironment)
    {
        for (UObject* Component : EnvironmentPair.Value.Components)
        {
            if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                if (IsValid(NiagaraComp))
                {
                    NiagaraComp->SetFloatParameter(TEXT("TransitionProgress"), NormalizedProgress);
                    NiagaraComp->SetFloatParameter(TEXT("DistortionStrength"), DistortionStrength);
                }
            }
        }
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "UpdateTransitionVisuals: Updated visuals with progress {0} and distortion {1}",
             NormalizedProgress, DistortionStrength);
}

void AAURACRONPCGLaneSystem::ApplyEnvironmentSpecificEffects(EAURACRONEnvironmentType Environment)
{
    // Aplicar efeitos específicos do ambiente usando APIs modernas UE 5.6
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Configurar parâmetros específicos do ambiente
    FLinearColor EnvironmentColor = FLinearColor::White;
    float EnvironmentIntensity = 1.0f;
    FString EnvironmentSoundCue;

    switch (Environment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            EnvironmentColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado
            EnvironmentIntensity = 1.2f;
            EnvironmentSoundCue = TEXT("/Game/AURACRON/Audio/Environments/AC_RadiantPlains_Ambient");
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            EnvironmentColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul celestial
            EnvironmentIntensity = 0.8f;
            EnvironmentSoundCue = TEXT("/Game/AURACRON/Audio/Environments/AC_ZephyrFirmament_Ambient");
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            EnvironmentColor = FLinearColor(0.8f, 0.4f, 0.9f, 1.0f); // Roxo espectral
            EnvironmentIntensity = 1.5f;
            EnvironmentSoundCue = TEXT("/Game/AURACRON/Audio/Environments/AC_PurgatoryRealm_Ambient");
            break;

        default:
            break;
    }

    // Aplicar efeitos aos componentes do ambiente atual
    const FAURACRONComponentArray* EnvironmentComponents = GeneratedComponentsByEnvironment.Find(Environment);
    if (EnvironmentComponents)
    {
        for (UObject* Component : EnvironmentComponents->Components)
        {
            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                if (IsValid(MeshComp))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                    {
                        DynamicMaterial->SetVectorParameterValue(TEXT("EnvironmentColor"), EnvironmentColor);
                        DynamicMaterial->SetScalarParameterValue(TEXT("EnvironmentIntensity"), EnvironmentIntensity);
                    }
                }
            }
        }
    }

    // Reproduzir áudio ambiente específico do ambiente
    if (!EnvironmentSoundCue.IsEmpty())
    {
        PlayEnvironmentAmbientSound(EnvironmentSoundCue);
    }

    UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "ApplyEnvironmentSpecificEffects: Applied effects for environment {0} with intensity {1}",
             static_cast<int32>(Environment), EnvironmentIntensity);
}

void AAURACRONPCGLaneSystem::PlayEnvironmentAmbientSound(const FString& SoundCuePath)
{
    // Reproduzir som ambiente específico do ambiente usando APIs modernas UE 5.6
    if (!IsValid(GetWorld()) || SoundCuePath.IsEmpty())
    {
        return;
    }

    // Carregar som usando StreamableManager
    if (StreamableManager)
    {
        FSoftObjectPath SoundSoftPath(SoundCuePath);
        if (USoundBase* AmbientSound = StreamableManager->LoadSynchronous<USoundBase>(SoundSoftPath))
        {
            // Parar som ambiente anterior se existir
            if (IsValid(CurrentAmbientAudioComponent))
            {
                CurrentAmbientAudioComponent->Stop();
            }

            // Criar novo componente de áudio
            CurrentAmbientAudioComponent = UGameplayStatics::SpawnSoundAtLocation(
                GetWorld(),
                AmbientSound,
                GetActorLocation(),
                FRotator::ZeroRotator,
                0.5f, // Volume
                1.0f, // Pitch
                0.0f, // Start time
                nullptr, // Attenuation
                nullptr, // Concurrency
                true // Auto destroy
            );

            UE_LOGFMT(LogAURACRONPCGLaneSystem, VeryVerbose, "PlayEnvironmentAmbientSound: Playing ambient sound {0}", SoundCuePath);
        }
        else
        {
            UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "PlayEnvironmentAmbientSound: Failed to load sound {0}", SoundCuePath);
        }
    }
}

// ========================================
// REPLICAÇÃO MODERNA UE 5.6 - NETWORKING
// ========================================

void AAURACRONPCGLaneSystem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades importantes usando APIs modernas UE 5.6
    DOREPLIFETIME(AAURACRONPCGLaneSystem, CurrentEnvironment);
    DOREPLIFETIME(AAURACRONPCGLaneSystem, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGLaneSystem, TargetEnvironment);
    DOREPLIFETIME(AAURACRONPCGLaneSystem, bIsTransitioning);
    DOREPLIFETIME_CONDITION(AAURACRONPCGLaneSystem, TransitionProgress, COND_SkipOwner);
    DOREPLIFETIME_CONDITION(AAURACRONPCGLaneSystem, JungleCamps, COND_InitialOnly);
}

void AAURACRONPCGLaneSystem::MulticastOnEnvironmentTransitionComplete_Implementation(EAURACRONEnvironmentType NewEnvironment)
{
    // RPC Multicast para notificar clientes sobre transição completa
    if (!HasAuthority())
    {
        // Atualizar estado local nos clientes
        CurrentEnvironment = NewEnvironment;

        // Aplicar efeitos visuais nos clientes
        ApplyEnvironmentSpecificEffects(NewEnvironment);

        UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "MulticastOnEnvironmentTransitionComplete: Client updated to environment {0}",
                 static_cast<int32>(NewEnvironment));
    }
}

void AAURACRONPCGLaneSystem::ServerRequestEnvironmentTransition_Implementation(EAURACRONEnvironmentType NewEnvironment, float Duration)
{
    // RPC Server para solicitar transição de ambiente
    if (HasAuthority())
    {
        // Validar solicitação
        if (NewEnvironment >= EAURACRONEnvironmentType::RadiantPlains && NewEnvironment <= EAURACRONEnvironmentType::PurgatoryRealm)
        {
            TransitionToEnvironment(NewEnvironment, Duration);
            UE_LOGFMT(LogAURACRONPCGLaneSystem, Log, "ServerRequestEnvironmentTransition: Initiated transition to environment {0}",
                     static_cast<int32>(NewEnvironment));
        }
        else
        {
            UE_LOGFMT(LogAURACRONPCGLaneSystem, Warning, "ServerRequestEnvironmentTransition: Invalid environment {0} requested",
                     static_cast<int32>(NewEnvironment));
        }
    }
}

bool AAURACRONPCGLaneSystem::ServerRequestEnvironmentTransition_Validate(EAURACRONEnvironmentType NewEnvironment, float Duration)
{
    // Validação do RPC Server
    return (NewEnvironment >= EAURACRONEnvironmentType::RadiantPlains &&
            NewEnvironment <= EAURACRONEnvironmentType::PurgatoryRealm &&
            Duration >= 0.0f && Duration <= 60.0f); // Máximo 60 segundos de transição
}
