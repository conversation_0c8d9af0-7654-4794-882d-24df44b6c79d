// AURACRONEditor.Target.cs
// Sistema de Sígilos AURACRON - Editor Target Configuration UE 5.6
// Configuração para build do editor

using UnrealBuildTool;

public class AURACRONEditorTarget : TargetRules
{
    public AURACRONEditorTarget(TargetInfo Target) : base(Target)
    {
        Type = TargetType.Editor;
        DefaultBuildSettings = BuildSettingsVersion.V5;
        IncludeOrderVersion = EngineIncludeOrderVersion.Unreal5_6;
        
        // Permitir override do ambiente de build para evitar conflitos
        bOverrideBuildEnvironment = true;
        
        // Módulos principais
        ExtraModuleNames.AddRange(new string[] { "AURACRON" });
        
        // Configurações de compilação para editor
        bUseUnityBuild = true;
        bUsePCHFiles = true;
        bBuildDeveloperTools = true;
        bBuildWithEditorOnlyData = true;
        bCompileWithPluginSupport = true;
        
        // Configurações específicas do editor
        bUseLoggingInShipping = true;
        bUseChecksInShipping = true;
        bCompileWithStatsWithoutEngine = true;
        
        // Configurações de rede para desenvolvimento multiplayer - UE 5.6 APIs
        bWithPushModel = true;
        bUseIris = true;                // Habilita o sistema Iris para replicação otimizada
        
        // Configurações de desenvolvimento
        bBuildTargetDeveloperTools = true;
        bCompileAgainstEngine = true;
        bCompileAgainstCoreUObject = true;
    }
}